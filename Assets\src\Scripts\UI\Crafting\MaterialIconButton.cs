using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using Sirenix.OdinInspector;
using TMPro;

/// <summary>
/// UI component for material icon buttons that grays out when material count is 0,
/// supports hover colors, and allows clicking to add materials to crafting slots.
/// </summary>
[RequireComponent(typeof(Button))]
public class MaterialIconButton : MonoBehaviour, IPointerEnterHandler, IPointerExitHandler
{
    [Title("Material Configuration")]
    [Required]
    [InfoBox("The crafting material data this button represents")]
    public CraftingMaterialData materialData;
    
    [Title("UI References")]
    [Required]
    [InfoBox("Button component (auto-found if not assigned)")]
    public Button button;
    
    [Required]
    [InfoBox("Image component for the material icon")]
    public Image iconImage;
    
    [Required]
    [InfoBox("Text component showing material count")]
    public TextMeshProUGUI countText;
    
    [Title("Color Configuration")]
    [InfoBox("Color when material count > 0")]
    public Color normalColor = Color.white;
    
    [InfoBox("Color when material count = 0")]
    public Color grayedOutColor = Color.gray;
    
    [InfoBox("Color when hovering over available material")]
    public Color hoverColor = Color.yellow;
    
    [InfoBox("Text color when material count > 0")]
    public Color normalTextColor = Color.white;
    
    [InfoBox("Text color when material count = 0")]
    public Color grayedOutTextColor = Color.gray;
    
    [Title("Animation Settings")]
    [InfoBox("Smooth color transition duration")]
    [Range(0f, 1f)]
    public float colorTransitionDuration = 0.2f;
    
    
    [Title("Debug")]
    [ReadOnly]
    [ShowInInspector]
    private int currentMaterialCount;
    
    [ReadOnly] 
    [ShowInInspector]
    private bool isAvailable;
    
    [ReadOnly]
    [ShowInInspector]
    private bool isHovering;
    
    // State
    private CraftingPanel craftingPanel;
    private Color currentIconColor;
    private Color currentTextColor;
    private bool isInitialized = false;
    
    // Events
    public System.Action<CraftingMaterialData> OnMaterialClicked;
    
    public bool IsAvailable => currentMaterialCount > 0;
    public CraftingMaterialData MaterialData => materialData;
    public int MaterialCount => currentMaterialCount;
    
    #region Unity Lifecycle
    
    private void Awake()
    {
        // Auto-find components if not assigned
        if (button == null)
            button = GetComponent<Button>();
            
        if (iconImage == null)
            iconImage = GetComponentInChildren<Image>();
            
        if (countText == null)
            countText = GetComponentInChildren<TextMeshProUGUI>();
            
        // Setup button click handler
        if (button != null)
        {
            button.onClick.AddListener(OnButtonClicked);
        }
        
        // Initialize colors
        currentIconColor = normalColor;
        currentTextColor = normalTextColor;
        
        ValidateSetup();
    }
    
    private void Start()
    {
        // Find CraftingPanel reference
        if (craftingPanel == null)
        {
            craftingPanel = FindFirstObjectByType<CraftingPanel>();
        }
        
        Initialize();
        RefreshDisplay();
    }
    
    private void OnDestroy()
    {
        // Stop any running coroutines first
        StopAllCoroutines();
        
        // Clean up events safely
        if (button != null)
            button.onClick.RemoveListener(OnButtonClicked);
            
        // Only unsubscribe if CraftingMaterialManager still exists and isn't being destroyed
        if (CraftingMaterialManager.Instance != null && !CraftingMaterialManager.Instance.Equals(null))
        {
            CraftingMaterialManager.OnMaterialInventoryChanged -= OnMaterialInventoryChanged;
        }
    }
    
    #endregion
    
    #region Initialization
    
    public void Initialize()
    {
        if (isInitialized)
            return;
            
        // Subscribe to material inventory changes
        if (CraftingMaterialManager.Instance != null)
        {
            CraftingMaterialManager.OnMaterialInventoryChanged += OnMaterialInventoryChanged;
        }
        
        // Set material icon if available
        if (materialData != null && iconImage != null)
        {
            if (materialData.icon != null)
            {
                iconImage.sprite = materialData.icon;
                iconImage.enabled = true;
            }
            else
            {
                Debug.LogWarning($"[MaterialIconButton] No icon assigned for material: {materialData.materialName}");
                iconImage.enabled = false;
            }
        }
        
        isInitialized = true;
        RefreshDisplay();
    }
    
    public void Initialize(CraftingMaterialData material, CraftingPanel panel)
    {
        materialData = material;
        craftingPanel = panel;
        Initialize();
    }
    
    private void ValidateSetup()
    {
        bool hasErrors = false;
        
        if (materialData == null)
        {
            Debug.LogError($"[MaterialIconButton] materialData is not assigned on {gameObject.name}!");
            hasErrors = true;
        }
        
        if (button == null)
        {
            Debug.LogError($"[MaterialIconButton] button component not found on {gameObject.name}!");
            hasErrors = true;
        }
        
        if (iconImage == null)
        {
            Debug.LogError($"[MaterialIconButton] iconImage is not assigned on {gameObject.name}!");
            hasErrors = true;
        }
        
        if (countText == null)
        {
            Debug.LogError($"[MaterialIconButton] countText is not assigned on {gameObject.name}!");
            hasErrors = true;
        }
        
        if (hasErrors)
        {
            Debug.LogError($"[MaterialIconButton] Setup validation failed for {gameObject.name}. Please assign missing references.");
        }
    }
    
    #endregion
    
    #region Display Management
    
    public void RefreshDisplay()
    {
        if (!isInitialized || materialData == null || !gameObject.activeInHierarchy)
            return;
            
        // Get current material count
        currentMaterialCount = CraftingMaterialManager.GetMaterialCount(materialData);
        isAvailable = currentMaterialCount > 0;
        
        // Update count text with forced refresh
        ForceUpdateCountText();
        
        // Update colors based on availability
        UpdateVisualState();
    }
    
    /// <summary>
    /// Forces the count text to be updated with the correct format.
    /// Can be called externally to fix text that was overwritten by other systems.
    /// </summary>
    public void ForceUpdateCountText()
    {
        if (countText != null && materialData != null)
        {
            countText.text = CraftingMaterialManager.FormatMaterialCount(currentMaterialCount);
        }
    }
    
    private void UpdateVisualState()
    {
        Color targetIconColor;
        Color targetTextColor;
        
        if (isHovering && isAvailable)
        {
            // Hover state (only when available)
            targetIconColor = hoverColor;
            targetTextColor = normalTextColor;
        }
        else if (isAvailable)
        {
            // Normal available state
            targetIconColor = normalColor;
            targetTextColor = normalTextColor;
        }
        else
        {
            // Grayed out state (count = 0)
            targetIconColor = grayedOutColor;
            targetTextColor = grayedOutTextColor;
        }
        
        // Apply colors (with smooth transition if desired)
        if (colorTransitionDuration > 0f && gameObject.activeInHierarchy)
        {
            // Use coroutine for smooth color transition (only if GameObject is active)
            StopAllCoroutines();
            StartCoroutine(TransitionToColor(targetIconColor, targetTextColor));
        }
        else
        {
            // Instant color change
            ApplyColors(targetIconColor, targetTextColor);
        }
        
        // Update button interactability
        if (button != null)
        {
            button.interactable = isAvailable;
        }
    }
    
    private void ApplyColors(Color iconColor, Color textColor)
    {
        currentIconColor = iconColor;
        currentTextColor = textColor;
        
        if (iconImage != null)
            iconImage.color = iconColor;
            
        if (countText != null)
            countText.color = textColor;
    }
    
    private System.Collections.IEnumerator TransitionToColor(Color targetIconColor, Color targetTextColor)
    {
        Color startIconColor = currentIconColor;
        Color startTextColor = currentTextColor;
        
        float elapsed = 0f;
        
        while (elapsed < colorTransitionDuration)
        {
            elapsed += Time.unscaledDeltaTime;
            float t = elapsed / colorTransitionDuration;
            
            Color currentIcon = Color.Lerp(startIconColor, targetIconColor, t);
            Color currentText = Color.Lerp(startTextColor, targetTextColor, t);
            
            ApplyColors(currentIcon, currentText);
            
            yield return null;
        }
        
        // Ensure final colors are applied
        ApplyColors(targetIconColor, targetTextColor);
    }
    
    
    #endregion
    
    #region Event Handlers
    
    private void OnButtonClicked()
    {
        if (!isAvailable || materialData == null)
        {
            Debug.Log($"[MaterialIconButton] Cannot use material: {materialData?.materialName ?? "null"} (count: {currentMaterialCount})");
            return;
        }
        
        Debug.Log($"[MaterialIconButton] Clicked on available material: {materialData.materialName} (count: {currentMaterialCount})");
        
        // Try to add material to first available slot
        if (craftingPanel != null)
        {
            bool success = TryAddMaterialToSlot();
            if (success)
            {
                Debug.Log($"[MaterialIconButton] Successfully added {materialData.materialName} to crafting slot");
            }
            else
            {
                Debug.Log($"[MaterialIconButton] No available slots for {materialData.materialName}");
            }
        }
        else
        {
            Debug.LogWarning("[MaterialIconButton] CraftingPanel reference not found!");
        }
        
        // Notify listeners
        OnMaterialClicked?.Invoke(materialData);
    }
    
    private bool TryAddMaterialToSlot()
    {
        if (craftingPanel == null || materialData == null)
            return false;
            
        // Find first empty slot
        for (int i = 0; i < 4; i++) // CraftingPanel has 4 slots
        {
            var materialInSlot = craftingPanel.GetMaterialInSlot(i);
            if (materialInSlot == null)
            {
                // Create material instance and add to slot
                var materialInstance = new CraftingMaterialInstance(materialData, 1);
                
                // Remove from inventory first
                if (CraftingMaterialManager.RemoveMaterial(materialData, 1))
                {
                    craftingPanel.SetMaterialInSlot(i, materialInstance);
                    return true;
                }
                else
                {
                    Debug.LogWarning($"[MaterialIconButton] Failed to remove {materialData.materialName} from inventory");
                    return false;
                }
            }
        }
        
        return false; // No empty slots available
    }
    
    public void OnPointerEnter(PointerEventData eventData)
    {
        if (!isAvailable)
            return;
            
        isHovering = true;
        UpdateVisualState();
        
        // Show tooltip
        if (craftingPanel != null && materialData != null)
        {
            string tooltipText = $"{materialData.materialName}\nAvailable: {currentMaterialCount}\n\nClick to add to crafting slot";
            craftingPanel.ShowTooltip(tooltipText, transform.position);
        }
    }
    
    public void OnPointerExit(PointerEventData eventData)
    {
        isHovering = false;
        UpdateVisualState();
        
        // Hide tooltip
        if (craftingPanel != null)
        {
            craftingPanel.HideTooltip();
        }
    }
    
    private void OnMaterialInventoryChanged()
    {
        // Only refresh if GameObject is active and valid
        if (this != null && gameObject != null && gameObject.activeInHierarchy)
        {
            RefreshDisplay();
        }
    }
    
    #endregion
    
    #region Debug Tools
    
    [Title("Debug Tools")]
    [Button("Refresh Display")]
    [PropertyOrder(20)]
    private void DebugRefreshDisplay()
    {
        RefreshDisplay();
        Debug.Log($"[MaterialIconButton] Refreshed display for {materialData?.materialName ?? "null"} - Count: {currentMaterialCount}");
    }
    
    [Button("Test Click")]
    [PropertyOrder(21)]
    private void DebugTestClick()
    {
        OnButtonClicked();
    }
    
    [Button("Add Test Material")]
    [PropertyOrder(22)]
    private void DebugAddTestMaterial()
    {
        if (materialData != null && CraftingMaterialManager.Instance != null)
        {
            CraftingMaterialManager.AddMaterial(materialData, 5);
            Debug.Log($"[MaterialIconButton] Added 5x {materialData.materialName} for testing");
        }
    }
    
    [Button("Remove Test Material")]
    [PropertyOrder(23)]
    private void DebugRemoveTestMaterial()
    {
        if (materialData != null && CraftingMaterialManager.Instance != null)
        {
            CraftingMaterialManager.RemoveMaterial(materialData, 1);
            Debug.Log($"[MaterialIconButton] Removed 1x {materialData.materialName} for testing");
        }
    }
    
    [Button("Force Update Count Text")]
    [PropertyOrder(24)]
    private void DebugForceUpdateCountText()
    {
        ForceUpdateCountText();
        Debug.Log($"[MaterialIconButton] Forced text update for {materialData?.materialName ?? "null"} - Text: '{countText?.text ?? "null"}'");
    }
    
    [Button("Simulate Text Overwrite")]
    [PropertyOrder(25)]
    private void DebugSimulateTextOverwrite()
    {
        if (countText != null)
        {
            string oldText = countText.text;
            countText.text = currentMaterialCount.ToString(); // Simulate MaterialSelectionCard overwriting
            Debug.Log($"[MaterialIconButton] Simulated text overwrite from '{oldText}' to '{countText.text}' - protection should fix this");
        }
    }
    
    #endregion
}