using UnityEngine;
using System.Collections.Generic;
using Sirenix.OdinInspector;

[RequireComponent(typeof(TilemapChunkManager))]
public class VegetationSpawnManager : MonoBehaviour
{
    [Title("Vegetation Spawn Manager")]
    [SerializeField, Required]
    [Tooltip("Prefab used for vegetation")]
    private GameObject vegetationPrefab;
    
    [SerializeField]
    [Tooltip("Parent transform for all spawned vegetation")]
    private Transform vegetationParent;
    
    [SerializeField]
    [Tooltip("Initial pool size for vegetation objects")]
    private int initialPoolSize = 100;
    
    [SerializeField, Range(0f, 1f)]
    [Tooltip("Minimum spacing between vegetation objects (in units)")]
    private float minimumSpacing = 0.3f;
    
    [SerializeField]
    [Tooltip("Maximum vegetation objects per chunk")]
    private int maxVegetationPerChunk = 50;
    
    [SerializeField]
    [Tooltip("Enable debug logging")]
    private bool debugMode = false;

    [Title("Smart Despawning System")]
    [SerializeField]
    [Tooltip("Enable smart vegetation despawning to improve performance")]
    private bool enableSmartDespawning = true;

    [SerializeField, Range(0, 3)]
    [Tooltip("Distance in chunks around player where vegetation stays active (0 = player chunk only, 1 = 3x3 area)")]
    [ShowIf("enableSmartDespawning")]
    private int vegetationActiveDistance = 1;

    [SerializeField, Range(0.05f, 1f)]
    [Tooltip("How often to check for vegetation despawning (in seconds)")]
    [ShowIf("enableSmartDespawning")]
    private float despawningUpdateInterval = 0.2f;

    [SerializeField]
    [Tooltip("Delay after camera transition before allowing despawning (prevents flickering)")]
    [ShowIf("enableSmartDespawning")]
    private float postTransitionDelay = 0.3f;

    [Header("Debug Settings")]
    [SerializeField]
    [Tooltip("Enable debug logging for despawning system")]
    [ShowIf("enableSmartDespawning")]
    private bool enableDebugLogging = false;

    [Title("Predictive Spawning System")]
    [SerializeField]
    [Tooltip("Enable predictive vegetation spawning (only spawn in current + predicted next chunk)")]
    private bool enablePredictiveSpawning = false;
    
    [SerializeField]
    [Tooltip("Fallback to original 3x3 spawning if predictive system fails")]
    [ShowIf("enablePredictiveSpawning")]
    private bool fallbackToOriginalSystem = false;
    
    // Singleton instance
    public static VegetationSpawnManager Instance { get; private set; }
    
    // References
    private TilemapChunkManager chunkManager;
    private BiomeManager biomeManager;
    private CinemachineCameraBounds cameraBounds;
    private LevelBasedChunkLockingSystem lockingSystem;
    private PlayerMovementPredictor movementPredictor;

    // Smart despawning system
    private HashSet<ChunkCoordinate> activeVegetationChunks = new HashSet<ChunkCoordinate>();
    private ChunkCoordinate lastPlayerChunk;
    private float nextDespawningUpdate = 0f;
    private float lastTransitionEndTime = 0f;
    private bool isInitialized = false;
    
    // Predictive spawning system
    private ChunkCoordinate predictedNextChunk;
    private bool hasPrediction = false;
    private bool isPredictiveSystemActive = false;
    
    // Tracking spawned vegetation per chunk
    private Dictionary<ChunkCoordinate, List<GameObject>> chunkVegetation = new Dictionary<ChunkCoordinate, List<GameObject>>();
    
    // Pool configuration tracking
    private bool poolConfigured = false;
    
    // Cached collections to avoid allocations
    private List<ChunkCoordinate> chunksToRemove = new List<ChunkCoordinate>();
    private List<VegetationPlacement> placementCache = new List<VegetationPlacement>(256);
    
    // Struct for vegetation placement data
    private struct VegetationPlacement
    {
        public Vector3 position;
        public Sprite[] sprites; // Array of sprites for multi-sprite support
        public float scale;
        public float rotation;
        public bool flipX;
        public Color color;
        public bool isHighGrass; // true = high grass prefab, false = low grass prefab
    }
    
    void Awake()
    {
        if (Instance == null)
            Instance = this;
        else if (Instance != this)
            Destroy(gameObject);
            
        chunkManager = GetComponent<TilemapChunkManager>();
        
        if (vegetationParent == null)
        {
            GameObject parentObj = new GameObject("Vegetation");
            parentObj.transform.SetParent(transform);
            vegetationParent = parentObj.transform;
        }
        
        // Validate vegetation prefab has required components
        ValidateVegetationPrefab(vegetationPrefab, "Vegetation");
    }

    private void ValidateVegetationPrefab(GameObject prefab, string prefabType)
    {
        if (prefab != null)
        {
            if (prefab.GetComponent<VegetationInstance>() == null)
            {
                Debug.LogError($"VegetationSpawnManager: {prefabType} prefab must have VegetationInstance component!");
            }
            if (prefab.GetComponent<YSortingController>() == null)
            {
                Debug.LogWarning($"VegetationSpawnManager: {prefabType} prefab should have YSortingController component!");
            }

            // Check for multi-sprite support
            MultiVegetationController multiController = prefab.GetComponent<MultiVegetationController>();
            if (multiController != null)
            {
                if (debugMode)
                {
                    Debug.Log($"VegetationSpawnManager: {prefabType} prefab has MultiVegetationController - multi-sprite variation enabled!");
                    Debug.Log($"VegetationSpawnManager: Child sprites should have individual YSortingController components for proper sorting");
                }
            }
            else
            {
                if (debugMode)
                    Debug.Log($"VegetationSpawnManager: {prefabType} prefab uses single sprite system (no MultiVegetationController found)");
            }
        }
        else
        {
            Debug.LogError($"VegetationSpawnManager: {prefabType} prefab is not assigned!");
        }
    }
    
    void OnDestroy()
    {
        // Unsubscribe from movement predictor events
        if (movementPredictor != null)
        {
            movementPredictor.OnMovementDirectionChanged -= OnMovementDirectionChanged;
            movementPredictor.OnChunkBoundaryApproaching -= OnChunkBoundaryApproaching;
            movementPredictor.OnChunkChanged -= OnPlayerChunkChanged;
        }
    }
    
    void Start()
    {
        // Get biome manager reference
        biomeManager = chunkManager.GetBiomeManager();

        if (biomeManager == null)
        {
            Debug.LogError("VegetationSpawnManager: Could not access BiomeManager from TilemapChunkManager!");
        }

        // Initialize camera bounds reference for smart despawning
        if (enableSmartDespawning)
        {
            cameraBounds = FindFirstObjectByType<CinemachineCameraBounds>();
            if (cameraBounds == null)
            {
                Debug.LogWarning("VegetationSpawnManager: CinemachineCameraBounds not found. Smart despawning will work without transition detection.");
            }
        }

        // Get locking system reference for locked chunk detection
        lockingSystem = FindFirstObjectByType<LevelBasedChunkLockingSystem>();
        if (lockingSystem == null)
        {
            Debug.LogWarning("VegetationSpawnManager: LevelBasedChunkLockingSystem not found. Vegetation will not despawn from locked chunks.");
        }

        // Configure pool on start
        ConfigurePool();

        // Initialize smart despawning system
        if (enableSmartDespawning)
        {
            InitializeSmartDespawning();
        }
        
        // Initialize predictive spawning system
        if (enablePredictiveSpawning)
        {
            InitializePredictiveSpawning();
        }
    }
    
    public void OnChunkGenerated(ChunkCoordinate coord, int seed)
    {
        if (vegetationPrefab == null)
        {
            if (debugMode)
                Debug.LogWarning("VegetationSpawnManager: Vegetation prefab not assigned!");
            return;
        }

        // Ensure biomeManager is initialized before proceeding
        if (biomeManager == null)
        {
            biomeManager = chunkManager.GetBiomeManager();
            if (biomeManager == null)
            {
                Debug.LogError("VegetationSpawnManager: BiomeManager is null, cannot generate vegetation!");
                return;
            }
        }

        // Check if we should use predictive spawning
        if (enablePredictiveSpawning && isPredictiveSystemActive)
        {
            // Only spawn vegetation if this chunk should have it according to predictive logic
            if (ShouldSpawnVegetationInChunk(coord))
            {
                if (debugMode)
                    Debug.Log($"[Predictive] Generating vegetation for chunk {coord} with seed {seed}");
                GenerateVegetationForChunk(coord, seed);
            }
            else
            {
                if (debugMode)
                    Debug.Log($"[Predictive] Skipping vegetation for chunk {coord} (not in active/predicted chunks)");
            }
        }
        else
        {
            // Original behavior: spawn vegetation in all chunks
            if (debugMode)
                Debug.Log($"[Original] Generating vegetation for chunk {coord} with seed {seed}");
            GenerateVegetationForChunk(coord, seed);
        }
    }
    
    public void OnChunkUnloaded(ChunkCoordinate coord)
    {
        if (debugMode)
            Debug.Log($"Unloading vegetation for chunk {coord}");

        DespawnVegetationForChunk(coord);
    }

    void Update()
    {
        if (!enableSmartDespawning || !isInitialized)
            return;

        // Check if it's time for a despawning update
        if (Time.time >= nextDespawningUpdate)
        {
            nextDespawningUpdate = Time.time + despawningUpdateInterval;
            UpdateVegetationVisibility();
        }
    }

    private void InitializeSmartDespawning()
    {
        if (chunkManager == null || chunkManager.GetPlayerTransform() == null)
        {
            Debug.LogWarning("VegetationSpawnManager: Cannot initialize smart despawning - missing chunk manager or player transform");
            return;
        }

        // Get initial player chunk
        Transform playerTransform = chunkManager.GetPlayerTransform();
        lastPlayerChunk = ChunkCoordinate.FromWorldPosition(
            playerTransform.position,
            chunkManager.GetChunkWidth(),
            chunkManager.GetChunkHeight()
        );

        // Calculate initial active vegetation chunks
        RecalculateActiveVegetationChunks();

        isInitialized = true;

        if (enableDebugLogging)
            Debug.Log($"VegetationSpawnManager: Smart despawning initialized with active distance {vegetationActiveDistance}");
    }

    private void UpdateVegetationVisibility()
    {
        // Don't despawn during camera transitions
        if (IsTransitioning())
        {
            if (enableDebugLogging)
                Debug.Log("VegetationSpawnManager: Skipping vegetation update - camera is transitioning");
            return;
        }

        // Don't despawn immediately after transition ends
        if (Time.time < lastTransitionEndTime + postTransitionDelay)
        {
            if (enableDebugLogging)
                Debug.Log("VegetationSpawnManager: Skipping vegetation update - post-transition delay");
            return;
        }

        Transform playerTransform = chunkManager.GetPlayerTransform();
        if (playerTransform == null)
            return;

        // Check if player moved to a different chunk
        ChunkCoordinate currentPlayerChunk = ChunkCoordinate.FromWorldPosition(
            playerTransform.position,
            chunkManager.GetChunkWidth(),
            chunkManager.GetChunkHeight()
        );

        if (currentPlayerChunk != lastPlayerChunk)
        {
            ChunkCoordinate previousChunk = lastPlayerChunk;
            lastPlayerChunk = currentPlayerChunk;
            
            // Check if the previous chunk (that we just left) is now locked
            // If so, despawn its vegetation since locked chunks never get distance-unloaded
            if (lockingSystem != null && lockingSystem.IsChunkLocked(previousChunk))
            {
                DespawnVegetationForChunk(previousChunk);
                if (enableDebugLogging)
                {
                    Debug.Log($"VegetationSpawnManager: Despawned vegetation from locked chunk {previousChunk} after player left");
                }
            }
            
            RecalculateActiveVegetationChunks();
            DespawnDistantVegetation();
        }
    }

    private bool IsTransitioning()
    {
        if (cameraBounds == null)
            return false;

        // Check if camera is currently transitioning
        bool wasTransitioning = cameraBounds.IsTransitioning;

        // If transition just ended, record the time
        if (!wasTransitioning && Time.time > lastTransitionEndTime + 0.1f)
        {
            // Check if we were transitioning in the previous frame by comparing with a small time window
            // This is a simple heuristic - in practice, you might want to add an event system
        }

        return wasTransitioning;
    }

    private void RecalculateActiveVegetationChunks()
    {
        activeVegetationChunks.Clear();

        if (enablePredictiveSpawning && isPredictiveSystemActive)
        {
            // Predictive spawning: only current chunk + predicted next chunk
            activeVegetationChunks.Add(lastPlayerChunk);
            
            if (hasPrediction)
            {
                activeVegetationChunks.Add(predictedNextChunk);
            }
            
            if (enableDebugLogging)
            {
                Debug.Log($"VegetationSpawnManager: [Predictive] Active chunks: {lastPlayerChunk}" + 
                         (hasPrediction ? $", {predictedNextChunk}" : " (no prediction)"));
            }
        }
        else
        {
            // Original system: calculate chunks within vegetation active distance
            for (int dx = -vegetationActiveDistance; dx <= vegetationActiveDistance; dx++)
            {
                for (int dy = -vegetationActiveDistance; dy <= vegetationActiveDistance; dy++)
                {
                    ChunkCoordinate coord = new ChunkCoordinate(
                        lastPlayerChunk.x + dx,
                        lastPlayerChunk.y + dy
                    );
                    activeVegetationChunks.Add(coord);
                }
            }

            if (enableDebugLogging)
            {
                Debug.Log($"VegetationSpawnManager: [Original] Recalculated active vegetation chunks around {lastPlayerChunk}. Total active: {activeVegetationChunks.Count}");
            }
        }
    }

    private void DespawnDistantVegetation()
    {
        chunksToRemove.Clear();

        // Find chunks with vegetation that are outside the active area
        foreach (var kvp in chunkVegetation)
        {
            ChunkCoordinate chunkCoord = kvp.Key;

            // Don't despawn if chunk is in active vegetation area
            if (activeVegetationChunks.Contains(chunkCoord))
                continue;

            // Don't despawn if chunk is about to be unloaded anyway (avoid double work)
            if (IsChunkScheduledForUnloading(chunkCoord))
                continue;

            chunksToRemove.Add(chunkCoord);
        }

        // Despawn vegetation in distant chunks
        int despawnedChunks = 0;
        foreach (var coord in chunksToRemove)
        {
            if (chunkVegetation.ContainsKey(coord))
            {
                DespawnVegetationForChunk(coord);
                despawnedChunks++;
            }
        }

        if (enableDebugLogging && despawnedChunks > 0)
        {
            Debug.Log($"VegetationSpawnManager: Despawned vegetation in {despawnedChunks} distant chunks");
        }
    }

    private bool IsChunkScheduledForUnloading(ChunkCoordinate coord)
    {
        // Check if chunk is beyond the unload distance
        // This prevents despawning vegetation in chunks that will be unloaded soon anyway
        int unloadDistance = chunkManager.GetViewDistance() + chunkManager.GetUnloadBufferDistance();

        int distanceX = Mathf.Abs(coord.x - lastPlayerChunk.x);
        int distanceY = Mathf.Abs(coord.y - lastPlayerChunk.y);
        int maxDistance = Mathf.Max(distanceX, distanceY);

        return maxDistance > unloadDistance;
    }
    
    private void InitializePredictiveSpawning()
    {
        // Find player movement predictor
        movementPredictor = FindFirstObjectByType<PlayerMovementPredictor>();
        if (movementPredictor == null)
        {
            Debug.LogWarning("VegetationSpawnManager: PlayerMovementPredictor not found. Falling back to original spawning system.");
            isPredictiveSystemActive = false;
            return;
        }
        
        // Subscribe to movement events
        movementPredictor.OnMovementDirectionChanged += OnMovementDirectionChanged;
        movementPredictor.OnChunkBoundaryApproaching += OnChunkBoundaryApproaching;
        movementPredictor.OnChunkChanged += OnPlayerChunkChanged;
        
        isPredictiveSystemActive = true;
        
        if (enableDebugLogging)
            Debug.Log("VegetationSpawnManager: Predictive spawning system initialized successfully.");
    }
    
    private void OnMovementDirectionChanged(Vector2 direction)
    {
        if (!isPredictiveSystemActive) return;
        
        // Update prediction
        UpdatePrediction();
        
        if (enableDebugLogging)
            Debug.Log($"VegetationSpawnManager: Movement direction changed to {direction}");
    }
    
    private void OnChunkBoundaryApproaching(ChunkCoordinate nextChunk)
    {
        if (!isPredictiveSystemActive) return;
        
        // Ensure vegetation is spawned in the predicted next chunk
        EnsureVegetationInChunk(nextChunk);
        
        if (enableDebugLogging)
            Debug.Log($"VegetationSpawnManager: Pre-spawning vegetation in chunk {nextChunk} (boundary approach)");
    }
    
    private void OnPlayerChunkChanged(ChunkCoordinate newChunk)
    {
        if (!isPredictiveSystemActive) return;
        
        // Update tracking and recalculate active chunks
        lastPlayerChunk = newChunk;
        UpdatePrediction();
        RecalculateActiveVegetationChunks();
        DespawnDistantVegetation();
        
        if (enableDebugLogging)
            Debug.Log($"VegetationSpawnManager: Player moved to chunk {newChunk}");
    }
    
    private void UpdatePrediction()
    {
        if (!isPredictiveSystemActive || movementPredictor == null) return;
        
        bool hadPrediction = hasPrediction;
        ChunkCoordinate oldPredictedChunk = predictedNextChunk;
        
        // Get new prediction from movement predictor (using properties, not methods)
        predictedNextChunk = movementPredictor.PredictedNextChunk;
        hasPrediction = movementPredictor.HasPrediction;
        
        // Check if prediction changed
        if (hadPrediction != hasPrediction || 
            (hasPrediction && oldPredictedChunk != predictedNextChunk))
        {
            // Recalculate active chunks with new prediction
            RecalculateActiveVegetationChunks();
            
            // If we have a new prediction, ensure vegetation is ready
            if (hasPrediction)
            {
                EnsureVegetationInChunk(predictedNextChunk);
            }
            
            if (enableDebugLogging)
            {
                Debug.Log($"VegetationSpawnManager: Prediction updated - hasPrediction: {hasPrediction}, nextChunk: {(hasPrediction ? predictedNextChunk.ToString() : "None")}");
            }
        }
    }
    
    private bool ShouldSpawnVegetationInChunk(ChunkCoordinate coord)
    {
        if (!enablePredictiveSpawning || !isPredictiveSystemActive)
            return true; // Fall back to original behavior
        
        // Only spawn in current chunk or predicted next chunk
        if (coord == lastPlayerChunk)
            return true;
            
        if (hasPrediction && coord == predictedNextChunk)
            return true;
            
        return false;
    }
    
    private void EnsureVegetationInChunk(ChunkCoordinate chunk)
    {
        // Check if vegetation is already spawned in this chunk
        if (chunkVegetation.ContainsKey(chunk) && chunkVegetation[chunk].Count > 0)
            return;
        
        // Generate vegetation for this chunk
        // Note: We will only call this for chunks that are about to be or are already loaded
        // The chunk generation process will call OnChunkGenerated which will handle this
        // For now, we'll use a simple approach and try to generate if not already present
        int chunkSeed = GetChunkSeed(chunk);
        GenerateVegetationForChunk(chunk, chunkSeed);
        
        if (enableDebugLogging)
            Debug.Log($"VegetationSpawnManager: Ensured vegetation in chunk {chunk}");
    }
    
    private int GetChunkSeed(ChunkCoordinate chunk)
    {
        // Match the seed generation from GenerateVegetationForChunk method
        // The baseSeed passed to OnChunkGenerated comes from TilemapChunkManager
        // For predictive spawning, we use a simplified deterministic approach
        // This will be deterministic based on chunk coordinates
        return chunk.GetHashCode() + 3000; // Same offset as used in GenerateVegetationForChunk
    }

    /// <summary>
    /// Debug method to log current vegetation system status
    /// </summary>
    [Button("Debug Vegetation Status", ButtonSizes.Medium)]
    public void DebugVegetationStatus()
    {
        if (!Application.isPlaying)
        {
            Debug.Log("VegetationSpawnManager: Debug info only available during play mode");
            return;
        }

        Debug.Log("=== VEGETATION SYSTEM STATUS ===");
        Debug.Log($"Smart Despawning: {(enableSmartDespawning ? "ENABLED" : "DISABLED")}");
        Debug.Log($"Predictive Spawning: {(enablePredictiveSpawning ? "ENABLED" : "DISABLED")}");
        Debug.Log($"Predictive System Active: {(isPredictiveSystemActive ? "YES" : "NO")}");
        Debug.Log($"Total Chunks with Vegetation: {chunkVegetation.Count}");

        int totalVegetationObjects = 0;
        foreach (var kvp in chunkVegetation)
        {
            totalVegetationObjects += kvp.Value.Count;
        }
        Debug.Log($"Total Vegetation Objects: {totalVegetationObjects}");

        if (enableSmartDespawning && isInitialized)
        {
            Debug.Log($"Current Player Chunk: {lastPlayerChunk}");
            Debug.Log($"Vegetation Active Distance: {vegetationActiveDistance}");
            Debug.Log($"Active Vegetation Chunks: {activeVegetationChunks.Count}");
            Debug.Log($"Camera Transitioning: {(IsTransitioning() ? "YES" : "NO")}");
            
            if (isPredictiveSystemActive)
            {
                Debug.Log($"Has Movement Prediction: {(hasPrediction ? "YES" : "NO")}");
                if (hasPrediction)
                {
                    Debug.Log($"Predicted Next Chunk: {predictedNextChunk}");
                }
            }

            // List chunks with vegetation
            Debug.Log("Chunks with vegetation:");
            foreach (var kvp in chunkVegetation)
            {
                bool isActive = activeVegetationChunks.Contains(kvp.Key);
                Debug.Log($"  {kvp.Key}: {kvp.Value.Count} objects {(isActive ? "(ACTIVE)" : "(DISTANT)")}");
            }
        }
        Debug.Log("================================");
    }

    [Button("Setup Predictive Spawning", ButtonSizes.Large)]
    public void SetupPredictiveSpawning()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Can only setup predictive spawning in play mode!");
            return;
        }

        Debug.Log("=== SETTING UP PREDICTIVE SPAWNING ===");
        
        // Find player
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player == null)
        {
            Debug.LogError("No GameObject with 'Player' tag found!");
            return;
        }

        // Check if PlayerMovementPredictor exists
        PlayerMovementPredictor predictor = player.GetComponent<PlayerMovementPredictor>();
        if (predictor == null)
        {
            Debug.Log("Adding PlayerMovementPredictor to Player GameObject...");
            predictor = player.AddComponent<PlayerMovementPredictor>();
        }
        else
        {
            Debug.Log("PlayerMovementPredictor already exists on Player GameObject.");
        }

        // Enable predictive spawning
        enablePredictiveSpawning = true;
        Debug.Log("Enabled predictive spawning flag.");

        // Re-initialize the system
        if (enablePredictiveSpawning)
        {
            InitializePredictiveSpawning();
        }

        Debug.Log($"Setup complete! Predictive system active: {isPredictiveSystemActive}");
        
        if (isPredictiveSystemActive)
        {
            Debug.Log("✅ PREDICTIVE SPAWNING IS NOW ACTIVE!");
            Debug.Log("The system will now only spawn vegetation in current + predicted chunks (max 2 chunks).");
        }
        else
        {
            Debug.LogError("❌ PREDICTIVE SPAWNING SETUP FAILED!");
            Debug.LogError("Check console for errors above.");
        }
        
        Debug.Log("===================================");
    }
    
    private void GenerateVegetationForChunk(ChunkCoordinate coord, int baseSeed)
    {
        // Use deterministic seed based on chunk coordinates and base seed
        int chunkSeed = baseSeed + coord.GetHashCode() + 3000; // Different offset than tiles
        Random.State originalState = Random.state;
        Random.InitState(chunkSeed);
        
        // Calculate chunk bounds
        int chunkWidth = chunkManager.GetChunkWidth();
        int chunkHeight = chunkManager.GetChunkHeight();
        Vector3 chunkWorldPos = coord.ToWorldPosition(chunkWidth, chunkHeight);
        
        // Clear placement cache
        placementCache.Clear();
        
        // Generate placement data first (deterministic)
        for (int x = 0; x < chunkWidth; x++)
        {
            for (int y = 0; y < chunkHeight; y++)
            {
                Vector3Int tilePos = new Vector3Int(
                    Mathf.FloorToInt(chunkWorldPos.x) + x,
                    Mathf.FloorToInt(chunkWorldPos.y) + y,
                    0
                );
                
                // Get biome for this position
                BiomeData biome = biomeManager.GetBiomeForWorldPosition(tilePos, chunkManager.GetChunkSize());
                
                if (biome != null && biome.HasVegetation())
                {
                    // Check if we should place vegetation here
                    if (ShouldPlaceVegetation(biome, tilePos))
                    {
                        // Use high grass vegetation sprites (only vegetation type now)
                        Sprite[] vegetationSprites = biome.HighGrassVegetationSprites;

                        if (vegetationSprites != null && vegetationSprites.Length > 0)
                        {
                            // Calculate position with slight randomization
                            Vector3 position = new Vector3(tilePos.x + 0.5f, tilePos.y + 0.5f, 0);
                            position.x += Random.Range(-0.4f, 0.4f);
                            position.y += Random.Range(-0.4f, 0.4f);

                            // Check minimum spacing
                            if (CheckMinimumSpacing(position, minimumSpacing))
                            {
                                VegetationPlacement placement = new VegetationPlacement
                                {
                                    position = position,
                                    sprites = vegetationSprites, // Store entire sprite array for multi-sprite support
                                    scale = Random.Range(biome.VegetationMinScale, biome.VegetationMaxScale),
                                    rotation = Random.Range(-biome.VegetationMaxRotation, biome.VegetationMaxRotation),
                                    flipX = Random.value < biome.VegetationFlipChance,
                                    color = biome.VegetationColor,
                                    isHighGrass = true // Always high grass now
                                };
                                
                                placementCache.Add(placement);
                                
                                // Check max per chunk limit
                                if (placementCache.Count >= maxVegetationPerChunk)
                                    break;
                            }
                        }
                    }
                }
            }
            
            if (placementCache.Count >= maxVegetationPerChunk)
                break;
        }
        
        // Restore random state
        Random.state = originalState;
        
        // Now spawn the vegetation using the pool manager
        SpawnVegetationFromPlacements(coord);
    }
    
    private bool ShouldPlaceVegetation(BiomeData biome, Vector3Int tilePos)
    {
        float vegetationChance = biome.VegetationDensity * biome.VegetationDensityMultiplier;
        
        // Apply noise-based clustering if enabled
        if (biome.UseVegetationClustering)
        {
            float noiseValue = Mathf.PerlinNoise(
                (tilePos.x * biome.VegetationNoiseScale) + biome.VegetationNoiseOffset.x,
                (tilePos.y * biome.VegetationNoiseScale) + biome.VegetationNoiseOffset.y
            );
            
            // Only place vegetation if noise is above threshold
            if (noiseValue < biome.VegetationNoiseThreshold)
            {
                return false;
            }
            
            // Modulate vegetation chance based on noise
            vegetationChance *= (noiseValue - biome.VegetationNoiseThreshold) / (1f - biome.VegetationNoiseThreshold);
        }
        
        return Random.value < vegetationChance;
    }
    
    private bool CheckMinimumSpacing(Vector3 position, float minimumSpacing)
    {
        if (minimumSpacing <= 0) return true;
        
        float spacingSqr = minimumSpacing * minimumSpacing;
        
        foreach (var placement in placementCache)
        {
            float distSqr = (placement.position - position).sqrMagnitude;
            if (distSqr < spacingSqr)
                return false;
        }
        
        return true;
    }
    
    private void SpawnVegetationFromPlacements(ChunkCoordinate coord)
    {
        if (placementCache.Count == 0) return;
        
        // Ensure we have a list for this chunk
        if (!chunkVegetation.ContainsKey(coord))
        {
            chunkVegetation[coord] = new List<GameObject>(placementCache.Count);
        }
        
        List<GameObject> vegetationList = chunkVegetation[coord];
        
        foreach (var placement in placementCache)
        {
            // Spawn from pool using the single vegetation prefab
            GameObject vegetation = PoolManager.Instance.Spawn(
                vegetationPrefab,
                placement.position,
                Quaternion.Euler(0, 0, placement.rotation),
                vegetationParent
            );
            
            if (vegetation != null)
            {
                // Cache VegetationInstance component (handles both single and multi-sprite systems)
                VegetationInstance instance = vegetation.GetComponent<VegetationInstance>();

                // Set sprites and color using VegetationInstance (handles both single and multi-sprite)
                if (instance != null)
                {
                    // Use the unified SetRandomSprites method (works for both single and multi-sprite)
                    instance.SetRandomSprites(placement.sprites, placement.color, placement.flipX);

                    // Set Y-sorting offset using VegetationInstance (handles child Y-sorting automatically)
                    BiomeData biome = biomeManager.GetBiomeForWorldPosition(
                        new Vector3Int(Mathf.RoundToInt(placement.position.x), Mathf.RoundToInt(placement.position.y), 0),
                        chunkManager.GetChunkSize()
                    );

                    if (biome != null)
                    {
                        instance.SetYSortingOffset(biome.VegetationYSortingOffset);
                    }
                }

                // Apply scale
                vegetation.transform.localScale = Vector3.one * placement.scale;

                // Track this vegetation object
                vegetationList.Add(vegetation);
            }
        }
        
        if (debugMode)
            Debug.Log($"Spawned {vegetationList.Count} vegetation objects in chunk {coord}");
    }
    
    private void ConfigurePool()
    {
        if (poolConfigured || vegetationPrefab == null) return;

        // Pre-warm pool by spawning and immediately despawning objects
        List<GameObject> tempObjects = new List<GameObject>(initialPoolSize);

        // Pre-warm vegetation pool
        for (int i = 0; i < initialPoolSize; i++)
        {
            GameObject obj = PoolManager.Instance.Spawn(
                vegetationPrefab,
                Vector3.zero,
                Quaternion.identity,
                vegetationParent
            );

            if (obj != null)
                tempObjects.Add(obj);
        }
        
        // Immediately return them to the pool
        foreach (var obj in tempObjects)
        {
            PoolManager.Instance.Despawn(obj);
        }
        
        poolConfigured = true;
        
        if (debugMode)
            Debug.Log($"Pre-warmed vegetation pool with {tempObjects.Count} objects");
    }
    
    private void DespawnVegetationForChunk(ChunkCoordinate coord)
    {
        if (!chunkVegetation.ContainsKey(coord))
            return;
            
        List<GameObject> vegetationList = chunkVegetation[coord];
        
        foreach (var vegetation in vegetationList)
        {
            if (vegetation != null)
            {
                PoolManager.Instance.Despawn(vegetation);
            }
        }
        
        vegetationList.Clear();
        chunkVegetation.Remove(coord);
    }
    
    public void ClearAllVegetation()
    {
        chunksToRemove.Clear();
        chunksToRemove.AddRange(chunkVegetation.Keys);
        
        foreach (var coord in chunksToRemove)
        {
            DespawnVegetationForChunk(coord);
        }
        
        chunkVegetation.Clear();
    }
    
    #if UNITY_EDITOR
    [Button("Debug: Spawn Test Vegetation", ButtonSizes.Large)]
    void DebugSpawnTestVegetation()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Can only test in play mode!");
            return;
        }
        
        ChunkCoordinate currentChunk = chunkManager.GetCurrentPlayerChunk();
        OnChunkGenerated(currentChunk, Random.Range(0, 10000));
    }
    
    [Button("Debug: Clear All Vegetation", ButtonSizes.Large)]
    void DebugClearAllVegetation()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Can only test in play mode!");
            return;
        }
        
        ClearAllVegetation();
        Debug.Log("Cleared all vegetation");
    }
    
    [Button("Debug: Log Vegetation Stats", ButtonSizes.Medium)]
    public void DebugLogStats()
    {
        Debug.Log("=== Vegetation Stats ===");
        Debug.Log($"Active Chunks: {chunkVegetation.Count}");
        
        int totalVegetation = 0;
        foreach (var kvp in chunkVegetation)
        {
            int count = kvp.Value.Count;
            totalVegetation += count;
            Debug.Log($"  Chunk {kvp.Key}: {count} vegetation objects");
        }
        
        Debug.Log($"Total Vegetation Objects: {totalVegetation}");
        Debug.Log($"Pool Configured: {poolConfigured}");
        Debug.Log("========================");
    }
    #endif
}