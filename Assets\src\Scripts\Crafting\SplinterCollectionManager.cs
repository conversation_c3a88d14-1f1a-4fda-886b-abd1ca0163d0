using UnityEngine;
using UnityEngine.Events;
using Sirenix.OdinInspector;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Manages splinter collection, storage, and conversion to materials.
/// Follows the established singleton manager pattern used throughout the project.
/// </summary>
public class SplinterCollectionManager : MonoBehaviour
{
    #region Singleton
    
    private static SplinterCollectionManager instance;
    public static SplinterCollectionManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindFirstObjectByType<SplinterCollectionManager>();
                if (instance == null)
                {
                    Debug.LogError("[SplinterCollectionManager] No instance found in scene! Please add SplinterCollectionManager to your scene.");
                }
            }
            return instance;
        }
    }
    
    #endregion
    
    #region Configuration
    
    [Title("Configuration")]
    [Required]
    [InfoBox("Splinter configuration asset that defines conversion rules")]
    [SerializeField] 
    private SplinterConfig splinterConfig;
    
    [Title("Debug Settings")]
    [SerializeField]
    [Tooltip("Enable debug logging for splinter collection")]
    private bool enableDebugLogging = false;
    
    #endregion
    
    #region State
    
    [Title("Current Splinter Counts")]
    [ShowInInspector, ReadOnly]
    [DictionaryDrawerSettings(KeyLabel = "Splinter Type", ValueLabel = "Count")]
    private Dictionary<SplinterType, int> splinterCounts = new Dictionary<SplinterType, int>();
    
    [ShowInInspector, ReadOnly]
    [InfoBox("Total splinters collected across all types")]
    private int TotalSplintersCollected => splinterCounts.Values.Sum();
    
    #endregion
    
    #region Events
    
    [Title("Events")]
    [InfoBox("Events fired when splinter counts change or materials are generated")]
    
    /// <summary>
    /// Fired when splinters are added (splinterType, newCount, addedAmount)
    /// </summary>
    public UnityEvent<SplinterType, int, int> OnSplintersAdded = new UnityEvent<SplinterType, int, int>();
    
    /// <summary>
    /// Fired when a material is generated from splinters (materialData, splinterType, splintersConsumed)
    /// </summary>
    public UnityEvent<CraftingMaterialData, SplinterType, int> OnMaterialGenerated = new UnityEvent<CraftingMaterialData, SplinterType, int>();
    
    /// <summary>
    /// Fired when any splinter count changes (for UI updates)
    /// </summary>
    public UnityEvent OnSplinterInventoryChanged = new UnityEvent();
    
    #endregion
    
    #region Unity Lifecycle
    
    private void Awake()
    {
        // Singleton setup
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeManager();
        }
        else if (instance != this)
        {
            Debug.LogWarning("[SplinterCollectionManager] Multiple instances detected. Destroying duplicate.");
            Destroy(gameObject);
        }
    }
    
    private void Start()
    {
        ValidateSetup();
    }
    
    private void OnDestroy()
    {
        // Clean up events
        OnSplintersAdded.RemoveAllListeners();
        OnMaterialGenerated.RemoveAllListeners();
        OnSplinterInventoryChanged.RemoveAllListeners();
        
        if (instance == this)
        {
            instance = null;
        }
    }
    
    #endregion
    
    #region Initialization
    
    private void InitializeManager()
    {
        // Initialize splinter counts for all types
        var allTypes = System.Enum.GetValues(typeof(SplinterType)).Cast<SplinterType>();
        foreach (var type in allTypes)
        {
            if (type.IsSingleType()) // Only single types, not None or combined flags
            {
                splinterCounts[type] = 0;
            }
        }
        
        if (enableDebugLogging)
        {
            Debug.Log($"[SplinterCollectionManager] Initialized with {splinterCounts.Count} splinter types");
        }
    }
    
    private void ValidateSetup()
    {
        bool hasErrors = false;
        
        if (splinterConfig == null)
        {
            Debug.LogError("[SplinterCollectionManager] splinterConfig is not assigned!");
            hasErrors = true;
        }
        
        if (CraftingMaterialManager.Instance == null)
        {
            Debug.LogError("[SplinterCollectionManager] CraftingMaterialManager not found in scene!");
            hasErrors = true;
        }
        
        if (!hasErrors && enableDebugLogging)
        {
            Debug.Log("[SplinterCollectionManager] Setup validation passed ✓");
        }
    }
    
    #endregion
    
    #region Public API - Static Methods
    
    /// <summary>
    /// Adds splinters of a specific type. Returns true if successful.
    /// </summary>
    public static bool AddSplinters(SplinterType splinterType, int amount)
    {
        if (Instance == null) return false;
        return Instance.AddSplintersInternal(splinterType, amount);
    }
    
    /// <summary>
    /// Gets the current count of a specific splinter type
    /// </summary>
    public static int GetSplinterCount(SplinterType splinterType)
    {
        if (Instance == null) return 0;
        return Instance.GetSplinterCountInternal(splinterType);
    }
    
    /// <summary>
    /// Gets the required splinters for a specific type to generate material
    /// </summary>
    public static int GetRequiredSplintersForType(SplinterType splinterType)
    {
        if (Instance == null || Instance.splinterConfig == null) return 50; // Default fallback
        return Instance.splinterConfig.GetRequiredSplintersForType(splinterType);
    }
    
    /// <summary>
    /// Gets the progress percentage (0-1) for a specific splinter type
    /// </summary>
    public static float GetProgressPercentage(SplinterType splinterType)
    {
        if (Instance == null) return 0f;
        
        int current = Instance.GetSplinterCountInternal(splinterType);
        int required = GetRequiredSplintersForType(splinterType);
        return required > 0 ? Mathf.Clamp01((float)current / required) : 0f;
    }
    
    /// <summary>
    /// Gets all configured splinter types
    /// </summary>
    public static List<SplinterType> GetAllSplinterTypes()
    {
        if (Instance == null || Instance.splinterConfig == null) 
        {
            return new List<SplinterType> { SplinterType.Red, SplinterType.Blue, SplinterType.Green, SplinterType.Yellow };
        }
        return Instance.splinterConfig.GetAllConfiguredSplinterTypes();
    }
    
    /// <summary>
    /// Checks if a splinter type is enabled and configured
    /// </summary>
    public static bool IsSplinterTypeEnabled(SplinterType splinterType)
    {
        if (Instance == null || Instance.splinterConfig == null) return false;
        return Instance.splinterConfig.IsSplinterTypeEnabled(splinterType);
    }
    
    /// <summary>
    /// Gets the CraftingMaterialData for a specific splinter type.
    /// This is the robust way to get material data without string matching!
    /// </summary>
    public static CraftingMaterialData GetMaterialDataForSplinterType(SplinterType splinterType)
    {
        if (Instance == null || Instance.splinterConfig == null) return null;
        return Instance.splinterConfig.GetMaterialForSplinterType(splinterType);
    }
    
    #endregion
    
    #region Internal Methods
    
    private bool AddSplintersInternal(SplinterType splinterType, int amount)
    {
        if (!splinterType.IsSingleType() || amount <= 0)
        {
            if (enableDebugLogging)
            {
                Debug.LogWarning($"[SplinterCollectionManager] Invalid parameters: Type={splinterType}, Amount={amount}");
            }
            return false;
        }
        
        if (!splinterCounts.ContainsKey(splinterType))
        {
            splinterCounts[splinterType] = 0;
        }
        
        int oldCount = splinterCounts[splinterType];
        splinterCounts[splinterType] += amount;
        
        if (enableDebugLogging)
        {
            Debug.Log($"[SplinterCollectionManager] Added {amount} {splinterType} splinters. Total: {splinterCounts[splinterType]}");
        }
        
        // Fire events
        OnSplintersAdded?.Invoke(splinterType, splinterCounts[splinterType], amount);
        OnSplinterInventoryChanged?.Invoke();
        
        // Check if we can generate materials
        CheckForMaterialGeneration(splinterType);
        
        return true;
    }
    
    private int GetSplinterCountInternal(SplinterType splinterType)
    {
        return splinterCounts.TryGetValue(splinterType, out int count) ? count : 0;
    }
    
    private void CheckForMaterialGeneration(SplinterType splinterType)
    {
        if (splinterConfig == null || !splinterConfig.IsSplinterTypeEnabled(splinterType))
        {
            return;
        }
        
        int currentCount = GetSplinterCountInternal(splinterType);
        int requiredCount = splinterConfig.GetRequiredSplintersForType(splinterType);
        
        if (currentCount >= requiredCount)
        {
            GenerateMaterialFromSplinters(splinterType, requiredCount);
        }
    }
    
    private void GenerateMaterialFromSplinters(SplinterType splinterType, int splintersToConsume)
    {
        var materialData = splinterConfig.GetMaterialForSplinterType(splinterType);
        if (materialData == null)
        {
            Debug.LogError($"[SplinterCollectionManager] No material configured for splinter type: {splinterType}");
            return;
        }
        
        // Consume splinters
        splinterCounts[splinterType] -= splintersToConsume;
        
        // Add material to inventory
        if (CraftingMaterialManager.Instance != null)
        {
            CraftingMaterialManager.AddMaterial(materialData, 1);
            
            if (enableDebugLogging)
            {
                Debug.Log($"[SplinterCollectionManager] Generated 1x {materialData.materialName} from {splintersToConsume} {splinterType} splinters");
            }
            
            // Fire events
            OnMaterialGenerated?.Invoke(materialData, splinterType, splintersToConsume);
            OnSplinterInventoryChanged?.Invoke();
        }
        else
        {
            Debug.LogError("[SplinterCollectionManager] CraftingMaterialManager not available for material generation!");
        }
    }
    
    #endregion
    
    #region Save/Load (Placeholder)
    
    /// <summary>
    /// Saves splinter data to persistent storage
    /// TODO: Implement save system integration
    /// </summary>
    public void SaveData()
    {
        // TODO: Implement save system
        if (enableDebugLogging)
        {
            Debug.Log("[SplinterCollectionManager] SaveData called (not implemented yet)");
        }
    }
    
    /// <summary>
    /// Loads splinter data from persistent storage
    /// TODO: Implement save system integration
    /// </summary>
    public void LoadData()
    {
        // TODO: Implement save system  
        if (enableDebugLogging)
        {
            Debug.Log("[SplinterCollectionManager] LoadData called (not implemented yet)");
        }
    }
    
    #endregion
    
    #region Debug Tools
    
    [Title("Debug Tools")]
    [Button("Add 10 Red Splinters")]
    [PropertyOrder(100)]
    private void DebugAddRedSplinters()
    {
        AddSplintersInternal(SplinterType.Red, 10);
    }
    
    [Button("Add 10 Blue Splinters")]
    [PropertyOrder(101)]
    private void DebugAddBlueSplinters()
    {
        AddSplintersInternal(SplinterType.Blue, 10);
    }
    
    [Button("Add 10 Green Splinters")]
    [PropertyOrder(102)]
    private void DebugAddGreenSplinters()
    {
        AddSplintersInternal(SplinterType.Green, 10);
    }
    
    [Button("Add 10 Yellow Splinters")]
    [PropertyOrder(103)]
    private void DebugAddYellowSplinters()
    {
        AddSplintersInternal(SplinterType.Yellow, 10);
    }
    
    [Button("Clear All Splinters")]
    [PropertyOrder(104)]
    private void DebugClearAllSplinters()
    {
        var types = splinterCounts.Keys.ToList();
        foreach (var type in types)
        {
            splinterCounts[type] = 0;
        }
        OnSplinterInventoryChanged?.Invoke();
        Debug.Log("[SplinterCollectionManager] All splinters cleared");
    }
    
    [Button("Log Current Status")]
    [PropertyOrder(105)]
    private void DebugLogStatus()
    {
        Debug.Log($"[SplinterCollectionManager] === SPLINTER STATUS ===");
        Debug.Log($"Total Splinters: {TotalSplintersCollected}");
        foreach (var kvp in splinterCounts)
        {
            int required = GetRequiredSplintersForType(kvp.Key);
            float progress = (float)kvp.Value / required * 100f;
            Debug.Log($"{kvp.Key}: {kvp.Value}/{required} ({progress:F1}%)");
        }
    }
    
    #endregion
}