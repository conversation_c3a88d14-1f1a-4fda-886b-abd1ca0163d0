fileFormatVersion: 2
guid: 1d4d955cfc791894a8da6847b807b961
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -1214866543446886210
    second: ice_c-sheet_0
  - first:
      213: 2730307790379568288
    second: ice_c-sheet_1
  - first:
      213: 9207825607311775311
    second: ice_c-sheet_2
  - first:
      213: -1257616907560727382
    second: ice_c-sheet_3
  - first:
      213: 5383720284983455537
    second: ice_c-sheet_4
  - first:
      213: -2320970291681506659
    second: ice_c-sheet_5
  - first:
      213: -6449447589670767335
    second: ice_c-sheet_6
  - first:
      213: -5355307442653569588
    second: ice_c-sheet_7
  - first:
      213: -2202573716559646395
    second: ice_c-sheet_8
  - first:
      213: 6304775552979421324
    second: ice_c-sheet_9
  - first:
      213: 5270094812423874390
    second: ice_c-sheet_10
  - first:
      213: -1829700676830387616
    second: ice_c-sheet_11
  - first:
      213: 7107216097410003295
    second: ice_c-sheet_12
  - first:
      213: 2332775353953436278
    second: ice_c-sheet_13
  - first:
      213: -1343194965586609361
    second: ice_c-sheet_14
  - first:
      213: -6022526606791716512
    second: ice_c-sheet_15
  - first:
      213: 1761510673066409239
    second: ice_c-sheet_16
  - first:
      213: 7763972679579315544
    second: ice_c-sheet_17
  - first:
      213: -108459228724473707
    second: ice_c-sheet_18
  - first:
      213: -8870109037728020632
    second: ice_c-sheet_19
  - first:
      213: -5884374933647868055
    second: ice_c-sheet_20
  - first:
      213: 8566941340601767073
    second: ice_c-sheet_21
  - first:
      213: 4156817381161566712
    second: ice_c-sheet_22
  - first:
      213: 1977883518497924138
    second: ice_c-sheet_23
  - first:
      213: -7646850135406890760
    second: ice_c-sheet_24
  - first:
      213: 7519739882505790133
    second: ice_c-sheet_25
  - first:
      213: 3566810099099353282
    second: ice_c-sheet_26
  - first:
      213: 5904044335383540801
    second: ice_c-sheet_27
  - first:
      213: -5816004745345662027
    second: ice_c-sheet_28
  - first:
      213: 7975398552471980437
    second: ice_c-sheet_29
  - first:
      213: 8228761552912199952
    second: ice_c-sheet_30
  - first:
      213: 2149460311282789940
    second: ice_c-sheet_31
  - first:
      213: 5779562405855843731
    second: ice_c-sheet_32
  - first:
      213: -4000474061248786760
    second: ice_c-sheet_33
  - first:
      213: -4780121011408485011
    second: ice_c-sheet_34
  - first:
      213: 8057463691124195976
    second: ice_c-sheet_35
  - first:
      213: -5714333234567086765
    second: ice_c-sheet_36
  - first:
      213: 4994770483341481359
    second: ice_c-sheet_37
  - first:
      213: 2775494178790334572
    second: ice_c-sheet_38
  - first:
      213: -257013314584955281
    second: ice_c-sheet_39
  - first:
      213: 8328738589316890811
    second: ice_c-sheet_40
  - first:
      213: -413932393481542097
    second: ice_c-sheet_41
  - first:
      213: -9031251315805118761
    second: ice_c-sheet_42
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 32
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: ice_c-sheet_0
      rect:
        serializedVersion: 2
        x: 341
        y: 13
        width: 38
        height: 77
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eb8775cb05de32fe0800000000000000
      internalID: -1214866543446886210
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_1
      rect:
        serializedVersion: 2
        x: 481
        y: 61
        width: 20
        height: 20
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0ac4e05387004e520800000000000000
      internalID: 2730307790379568288
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_2
      rect:
        serializedVersion: 2
        x: 619
        y: 75
        width: 13
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f46509dca94c8cf70800000000000000
      internalID: 9207825607311775311
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_3
      rect:
        serializedVersion: 2
        x: 681
        y: 58
        width: 25
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: aac4151f51c0c8ee0800000000000000
      internalID: -1257616907560727382
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_4
      rect:
        serializedVersion: 2
        x: 721
        y: 18
        width: 80
        height: 60
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 13fe0843311d6ba40800000000000000
      internalID: 5383720284983455537
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_5
      rect:
        serializedVersion: 2
        x: 814
        y: 12
        width: 52
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d9ac61611d14acfd0800000000000000
      internalID: -2320970291681506659
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_6
      rect:
        serializedVersion: 2
        x: 893
        y: 10
        width: 51
        height: 68
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 915bc2c7695fe76a0800000000000000
      internalID: -6449447589670767335
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_7
      rect:
        serializedVersion: 2
        x: 970
        y: 4
        width: 54
        height: 74
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ccdd0a921402ea5b0800000000000000
      internalID: -5355307442653569588
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_8
      rect:
        serializedVersion: 2
        x: 1048
        y: 6
        width: 56
        height: 72
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 541b9d0efd2ee61e0800000000000000
      internalID: -2202573716559646395
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_9
      rect:
        serializedVersion: 2
        x: 1127
        y: 3
        width: 59
        height: 75
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c88c7e716ff0f7750800000000000000
      internalID: 6304775552979421324
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_10
      rect:
        serializedVersion: 2
        x: 1205
        y: 4
        width: 62
        height: 74
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 657672b4f43232940800000000000000
      internalID: 5270094812423874390
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_11
      rect:
        serializedVersion: 2
        x: 1286
        y: 0
        width: 63
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 062ab38bde89b96e0800000000000000
      internalID: -1829700676830387616
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_12
      rect:
        serializedVersion: 2
        x: 1366
        y: 0
        width: 66
        height: 78
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f51bf0c8267e1a260800000000000000
      internalID: 7107216097410003295
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_13
      rect:
        serializedVersion: 2
        x: 1447
        y: 15
        width: 65
        height: 63
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6724e2923deaf5020800000000000000
      internalID: 2332775353953436278
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_14
      rect:
        serializedVersion: 2
        x: 1534
        y: 15
        width: 57
        height: 63
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f2bfd1c9b430c5de0800000000000000
      internalID: -1343194965586609361
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_15
      rect:
        serializedVersion: 2
        x: 1614
        y: 15
        width: 50
        height: 63
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 06157d7b5ffab6ca0800000000000000
      internalID: -6022526606791716512
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_16
      rect:
        serializedVersion: 2
        x: 109
        y: 56
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 71dbe20df94227810800000000000000
      internalID: 1761510673066409239
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_17
      rect:
        serializedVersion: 2
        x: 127
        y: 45
        width: 13
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 85da92c440c2fbb60800000000000000
      internalID: 7763972679579315544
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_18
      rect:
        serializedVersion: 2
        x: 185
        y: 53
        width: 13
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 590d347f7ecae7ef0800000000000000
      internalID: -108459228724473707
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_19
      rect:
        serializedVersion: 2
        x: 203
        y: 41
        width: 20
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8674d3773da07e480800000000000000
      internalID: -8870109037728020632
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_20
      rect:
        serializedVersion: 2
        x: 262
        y: 36
        width: 33
        height: 35
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 96fb4b2bc20865ea0800000000000000
      internalID: -5884374933647868055
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_21
      rect:
        serializedVersion: 2
        x: 418
        y: 9
        width: 20
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1a45d2a72c3e3e670800000000000000
      internalID: 8566941340601767073
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_22
      rect:
        serializedVersion: 2
        x: 578
        y: 24
        width: 52
        height: 39
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8f10e48626bffa930800000000000000
      internalID: 4156817381161566712
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_23
      rect:
        serializedVersion: 2
        x: 661
        y: 24
        width: 31
        height: 39
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a242b2fa49ad27b10800000000000000
      internalID: 1977883518497924138
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_24
      rect:
        serializedVersion: 2
        x: 50
        y: 48
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8f0bc6c015ee0e590800000000000000
      internalID: -7646850135406890760
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_25
      rect:
        serializedVersion: 2
        x: 177
        y: 29
        width: 20
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5b26814f49b7b5860800000000000000
      internalID: 7519739882505790133
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_26
      rect:
        serializedVersion: 2
        x: 504
        y: 16
        width: 17
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2c0c71a23eadf7130800000000000000
      internalID: 3566810099099353282
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_27
      rect:
        serializedVersion: 2
        x: 23
        y: 36
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 14471b4ca016fe150800000000000000
      internalID: 5904044335383540801
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_28
      rect:
        serializedVersion: 2
        x: 100
        y: 33
        width: 13
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5b38fa5af76694fa0800000000000000
      internalID: -5816004745345662027
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_29
      rect:
        serializedVersion: 2
        x: 278
        y: 17
        width: 20
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 599c14e5abe4eae60800000000000000
      internalID: 7975398552471980437
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_30
      rect:
        serializedVersion: 2
        x: 542
        y: 21
        width: 18
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 01139b6d80f623270800000000000000
      internalID: 8228761552912199952
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_31
      rect:
        serializedVersion: 2
        x: 1286
        y: 31
        width: 8
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 43eafc8c3ca64dd10800000000000000
      internalID: 2149460311282789940
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_32
      rect:
        serializedVersion: 2
        x: 124
        y: 24
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3955d3ef261253050800000000000000
      internalID: 5779562405855843731
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_33
      rect:
        serializedVersion: 2
        x: 201
        y: 21
        width: 13
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8be0a1669067b78c0800000000000000
      internalID: -4000474061248786760
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_34
      rect:
        serializedVersion: 2
        x: 766
        y: 17
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d65cd9b244999adb0800000000000000
      internalID: -4780121011408485011
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_35
      rect:
        serializedVersion: 2
        x: 264
        y: 16
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 882c01c698cd1df60800000000000000
      internalID: 8057463691124195976
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_36
      rect:
        serializedVersion: 2
        x: 1298
        y: 10
        width: 11
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 35d7b61f23c92b0b0800000000000000
      internalID: -5714333234567086765
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_37
      rect:
        serializedVersion: 2
        x: 321
        y: 0
        width: 10
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f85dc2a8a4df05540800000000000000
      internalID: 4994770483341481359
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_38
      rect:
        serializedVersion: 2
        x: 1074
        y: 1
        width: 8
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c6066fa1049848620800000000000000
      internalID: 2775494178790334572
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_39
      rect:
        serializedVersion: 2
        x: 1154
        y: 0
        width: 9
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f663e9980c7ee6cf0800000000000000
      internalID: -257013314584955281
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_40
      rect:
        serializedVersion: 2
        x: 1233
        y: 0
        width: 11
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bb042539e9f959370800000000000000
      internalID: 8328738589316890811
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_41
      rect:
        serializedVersion: 2
        x: 1237
        y: 2
        width: 13
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f2661856eaa614af0800000000000000
      internalID: -413932393481542097
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ice_c-sheet_42
      rect:
        serializedVersion: 2
        x: 1306
        y: 5
        width: 6
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7de182427cc8aa280800000000000000
      internalID: -9031251315805118761
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      ice_c-sheet_0: -1214866543446886210
      ice_c-sheet_1: 2730307790379568288
      ice_c-sheet_10: 5270094812423874390
      ice_c-sheet_11: -1829700676830387616
      ice_c-sheet_12: 7107216097410003295
      ice_c-sheet_13: 2332775353953436278
      ice_c-sheet_14: -1343194965586609361
      ice_c-sheet_15: -6022526606791716512
      ice_c-sheet_16: 1761510673066409239
      ice_c-sheet_17: 7763972679579315544
      ice_c-sheet_18: -108459228724473707
      ice_c-sheet_19: -8870109037728020632
      ice_c-sheet_2: 9207825607311775311
      ice_c-sheet_20: -5884374933647868055
      ice_c-sheet_21: 8566941340601767073
      ice_c-sheet_22: 4156817381161566712
      ice_c-sheet_23: 1977883518497924138
      ice_c-sheet_24: -7646850135406890760
      ice_c-sheet_25: 7519739882505790133
      ice_c-sheet_26: 3566810099099353282
      ice_c-sheet_27: 5904044335383540801
      ice_c-sheet_28: -5816004745345662027
      ice_c-sheet_29: 7975398552471980437
      ice_c-sheet_3: -1257616907560727382
      ice_c-sheet_30: 8228761552912199952
      ice_c-sheet_31: 2149460311282789940
      ice_c-sheet_32: 5779562405855843731
      ice_c-sheet_33: -4000474061248786760
      ice_c-sheet_34: -4780121011408485011
      ice_c-sheet_35: 8057463691124195976
      ice_c-sheet_36: -5714333234567086765
      ice_c-sheet_37: 4994770483341481359
      ice_c-sheet_38: 2775494178790334572
      ice_c-sheet_39: -257013314584955281
      ice_c-sheet_4: 5383720284983455537
      ice_c-sheet_40: 8328738589316890811
      ice_c-sheet_41: -413932393481542097
      ice_c-sheet_42: -9031251315805118761
      ice_c-sheet_5: -2320970291681506659
      ice_c-sheet_6: -6449447589670767335
      ice_c-sheet_7: -5355307442653569588
      ice_c-sheet_8: -2202573716559646395
      ice_c-sheet_9: 6304775552979421324
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
