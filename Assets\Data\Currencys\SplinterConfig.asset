%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aa2e920a43e398b478052759458f0e5a, type: 3}
  m_Name: SplinterConfig
  m_EditorClassIdentifier: Assembly-CSharp::SplinterConfig
  defaultSplinterRequirement: 50
  splinterMappings:
  - splinterType: 32
    materialData: {fileID: 11400000, guid: c3a4f2bf1f84d0f4f893baa5643358ce, type: 2}
    splinterRequirement: 50
  - splinterType: 16
    materialData: {fileID: 11400000, guid: f9a8b7c6e5d4c3b2a1908f7e6d5c4b3a, type: 2}
    splinterRequirement: 50
  - splinterType: 1
    materialData: {fileID: 11400000, guid: 3eb0182274935c544af87541ca89b86e, type: 2}
    splinterRequirement: 50
  - splinterType: 2
    materialData: {fileID: 11400000, guid: 23de48310b87f7145924fb6163101a7f, type: 2}
    splinterRequirement: 50
  - splinterType: 8
    materialData: {fileID: 11400000, guid: b403102fd83a63c45a0b67b4341a0a78, type: 2}
    splinterRequirement: 50
  - splinterType: 4
    materialData: {fileID: 11400000, guid: a457a5d8462215b44af4330301cf6d04, type: 2}
    splinterRequirement: 50
  baseSplinterDropChance: 0.75
  minSplintersPerDrop: 1
  maxSplintersPerDrop: 5
