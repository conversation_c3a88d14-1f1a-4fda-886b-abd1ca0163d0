%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc7afdaae02bac047acf72aabe6a2a9c, type: 3}
  m_Name: LightningArrow
  m_EditorClassIdentifier: Assembly-CSharp::RogueLike.Items.SkillGemData
  icon: {fileID: -5517416819149128043, guid: 8090be05bc96bad4a808688a4cc25ac0, type: 3}
  backgroundIcon: {fileID: 0}
  foregroundIcon: {fileID: 0}
  gemName: Lightning Arrow
  description: Fires electrified arrows that chain lightning between nearby enemies,
    shocking them with devastating electrical damage.
  rarity: 2
  requiresUnlock: 0
  unlockCondition: {fileID: 0}
  skillType: 1
  gemTags: 562
  skillPrefab: {fileID: 2340940985034927489, guid: eb533a8a5c7cb7840bedf513b94e9621, type: 3}
  baseDamage: 6
  cooldown: 1
  manaCost: 10
  projectileSpeed: 8.5
  duration: 1
  waveAmplitude: 0.08
  waveFrequency: 8
  serpentineDelay: 0.2
  projectileCount: 1
  projectileDelay: 0.1
  orbitRadius: 2
  rotationSpeed: 180
  bladeCount: 3
  targetGroundPosition: 1
  attackSpeedMultiplier: 1
  projectileLayer: 8
  critChance: 5
  critMultiplier: 2
  damageType: 3
  ailmentChance: 50
  intrinsicProjectileCount: 1
  intrinsicSpreadAngle: 20
  intrinsicUseRandomSpread: 0
  intrinsicHasPierce: 0
  intrinsicHasChain: 0
  intrinsicChainCount: 2
  projectileSpeedVariation: 1
  serpentineAmplitudeVariation: 0.2
  serpentineFrequencyVariation: 0.1
  ignitePercent: 0.122
  igniteDuration: 4
  igniteTickInterval: 0.5
  freezeSlowAmount: 0.5
  freezeDuration: 2
  bleedPercent: 0.15
  bleedDuration: 6
  bleedTickInterval: 1
  shockChainDamage: 0.1
  shockChainRange: 3
  shockDuration: 2
