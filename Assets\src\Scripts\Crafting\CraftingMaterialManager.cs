using UnityEngine;
using Sirenix.OdinInspector;
using System;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Singleton manager for crafting materials state and operations.
/// Handles multiple material types with stacking support.
/// Evolved from the original CurrencyManager to support crafting system.
/// </summary>
public class CraftingMaterialManager : MonoBehaviour
{
    [Title("Material Management")]
    [ShowInInspector, ReadOnly]
    [PropertyOrder(-1)]
    private static CraftingMaterialManager instance;
    
    [SerializeField]
    [PropertyOrder(1)]
    [ListDrawerSettings(ShowIndexLabels = true, DraggableItems = false)]
    private List<CraftingMaterialInstance> materialInventory = new List<CraftingMaterialInstance>();
    
    [Title("Debug Settings")]
    [SerializeField]
    [PropertyOrder(10)]
    private bool enableDebugLogging = false;
    
    // Events
    public static event Action<CraftingMaterialData, int> OnMaterialAdded;
    public static event Action<CraftingMaterialData, int> OnMaterialRemoved;
    public static event Action OnMaterialInventoryChanged;
    
    // Static Properties
    public static CraftingMaterialManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<CraftingMaterialManager>();
                if (instance == null)
                {
                    GameObject managerObject = new GameObject("CraftingMaterialManager");
                    instance = managerObject.AddComponent<CraftingMaterialManager>();
                }
            }
            return instance;
        }
    }
    
    public static List<CraftingMaterialInstance> MaterialInventory => new List<CraftingMaterialInstance>(Instance.materialInventory);
    public static int GetMaterialCount(CraftingMaterialData materialData) => Instance.GetMaterialCountInternal(materialData);
    public static bool HasMaterial(CraftingMaterialData materialData, int amount = 1) => Instance.GetMaterialCountInternal(materialData) >= amount;
    
    #region Unity Lifecycle
    
    private void Awake()
    {
        if (instance == null)
        {
            instance = this;
            LoadMaterials();
        }
        else if (instance != this)
        {
            if (enableDebugLogging)
                Debug.LogWarning("[CraftingMaterialManager] Duplicate instance detected, destroying...");
            Destroy(gameObject);
        }
    }
    
    private void OnDestroy()
    {
        if (instance == this)
        {
            SaveMaterials();
        }
    }
    
    private void OnApplicationPause(bool pauseStatus)
    {
        if (pauseStatus)
        {
            SaveMaterials();
        }
    }
    
    private void OnApplicationFocus(bool hasFocus)
    {
        if (!hasFocus)
        {
            SaveMaterials();
        }
    }
    
    #endregion
    
    #region Material Operations
    
    /// <summary>
    /// Add materials to the inventory
    /// </summary>
    /// <param name="materialData">Material type to add</param>
    /// <param name="amount">Amount to add (must be positive)</param>
    public static void AddMaterial(CraftingMaterialData materialData, int amount)
    {
        if (materialData == null)
        {
            Debug.LogWarning("[CraftingMaterialManager] Attempted to add null material");
            return;
        }
        
        if (amount <= 0)
        {
            Debug.LogWarning($"[CraftingMaterialManager] Attempted to add invalid amount: {amount}");
            return;
        }
        
        Instance.AddMaterialInternal(materialData, amount);
    }
    
    /// <summary>
    /// Remove materials from inventory
    /// </summary>
    /// <param name="materialData">Material type to remove</param>
    /// <param name="amount">Amount to remove</param>
    /// <returns>True if transaction was successful</returns>
    public static bool RemoveMaterial(CraftingMaterialData materialData, int amount)
    {
        if (materialData == null)
        {
            Debug.LogWarning("[CraftingMaterialManager] Attempted to remove null material");
            return false;
        }
        
        if (amount <= 0)
        {
            Debug.LogWarning($"[CraftingMaterialManager] Attempted to remove invalid amount: {amount}");
            return false;
        }
        
        return Instance.RemoveMaterialInternal(materialData, amount);
    }
    
    /// <summary>
    /// Set material count to a specific amount (debug/cheat function)
    /// </summary>
    /// <param name="materialData">Material type</param>
    /// <param name="amount">Target amount</param>
    public static void SetMaterial(CraftingMaterialData materialData, int amount)
    {
        if (materialData == null)
        {
            Debug.LogWarning("[CraftingMaterialManager] Attempted to set null material");
            return;
        }
        
        if (amount < 0)
        {
            Debug.LogWarning($"[CraftingMaterialManager] Attempted to set negative amount: {amount}");
            return;
        }
        
        Instance.SetMaterialInternal(materialData, amount);
    }
    
    /// <summary>
    /// Get all available material types
    /// </summary>
    /// <returns>List of material data that player has in inventory</returns>
    public static List<CraftingMaterialData> GetAvailableMaterialTypes()
    {
        return Instance.materialInventory
            .Where(mat => mat.materialDataTemplate != null && mat.stackCount > 0)
            .Select(mat => mat.materialDataTemplate)
            .Distinct()
            .ToList();
    }
    
    /// <summary>
    /// Get all material instances from the inventory
    /// </summary>
    /// <returns>List of all material instances with stack count > 0</returns>
    public static List<CraftingMaterialInstance> GetAllMaterials()
    {
        return Instance.materialInventory
            .Where(mat => mat.materialDataTemplate != null && mat.stackCount > 0)
            .ToList();
    }
    
    /// <summary>
    /// Get materials grouped by type for UI display
    /// </summary>
    /// <returns>Dictionary mapping material data to total count</returns>
    public static Dictionary<CraftingMaterialData, int> GetMaterialSummary()
    {
        var summary = new Dictionary<CraftingMaterialData, int>();
        
        foreach (var material in Instance.materialInventory)
        {
            if (material.materialDataTemplate != null && material.stackCount > 0)
            {
                if (summary.ContainsKey(material.materialDataTemplate))
                {
                    summary[material.materialDataTemplate] += material.stackCount;
                }
                else
                {
                    summary[material.materialDataTemplate] = material.stackCount;
                }
            }
        }
        
        return summary;
    }
    
    /// <summary>
    /// Formats material count with standardized "x [count]" format.
    /// Central formatting method to ensure consistency across all UI components.
    /// </summary>
    /// <param name="count">Material count to format</param>
    /// <returns>Formatted string like "x 5" or "x 0"</returns>
    public static string FormatMaterialCount(int count)
    {
        return $"x {count}";
    }
    
    #endregion
    
    #region Internal Operations
    
    private void AddMaterialInternal(CraftingMaterialData materialData, int amount)
    {
        int remainingAmount = amount;
        
        // Try to add to existing stacks first
        var existingStacks = materialInventory.Where(mat => mat.CanStackWith(materialData)).ToList();
        foreach (var stack in existingStacks)
        {
            if (remainingAmount <= 0) break;
            
            int overflow = stack.AddToStack(remainingAmount);
            remainingAmount = overflow;
        }
        
        // Create new stacks for remaining amount
        while (remainingAmount > 0)
        {
            int stackSize = Mathf.Min(remainingAmount, materialData.maxStackSize);
            var newStack = new CraftingMaterialInstance(materialData, stackSize);
            materialInventory.Add(newStack);
            remainingAmount -= stackSize;
        }
        
        CleanupInventory();
        
        if (enableDebugLogging)
        {
            Debug.Log($"[CraftingMaterialManager] Added {amount} {materialData.materialName}. Total: {GetMaterialCountInternal(materialData)}");
        }
        
        OnMaterialAdded?.Invoke(materialData, amount);
        OnMaterialInventoryChanged?.Invoke();
    }
    
    private bool RemoveMaterialInternal(CraftingMaterialData materialData, int amount)
    {
        if (!HasMaterial(materialData, amount))
        {
            if (enableDebugLogging)
            {
                Debug.LogWarning($"[CraftingMaterialManager] Insufficient materials. Required: {amount}, Available: {GetMaterialCountInternal(materialData)}");
            }
            return false;
        }
        
        int remainingToRemove = amount;
        var stacksToRemove = new List<CraftingMaterialInstance>();
        
        // Remove from stacks (prioritize smaller stacks first)
        var stacks = materialInventory
            .Where(mat => mat.materialDataTemplate == materialData && mat.stackCount > 0)
            .OrderBy(mat => mat.stackCount)
            .ToList();
            
        foreach (var stack in stacks)
        {
            if (remainingToRemove <= 0) break;
            
            int removed = stack.RemoveFromStack(remainingToRemove);
            remainingToRemove -= removed;
            
            if (stack.stackCount <= 0)
            {
                stacksToRemove.Add(stack);
            }
        }
        
        // Remove empty stacks
        foreach (var stack in stacksToRemove)
        {
            materialInventory.Remove(stack);
        }
        
        if (enableDebugLogging)
        {
            Debug.Log($"[CraftingMaterialManager] Removed {amount} {materialData.materialName}. Remaining: {GetMaterialCountInternal(materialData)}");
        }
        
        OnMaterialRemoved?.Invoke(materialData, amount);
        OnMaterialInventoryChanged?.Invoke();
        
        return true;
    }
    
    private void SetMaterialInternal(CraftingMaterialData materialData, int amount)
    {
        int currentAmount = GetMaterialCountInternal(materialData);
        
        // Remove all current materials of this type
        materialInventory.RemoveAll(mat => mat.materialDataTemplate == materialData);
        
        // Add the target amount
        if (amount > 0)
        {
            AddMaterialInternal(materialData, amount);
        }
        
        if (enableDebugLogging)
        {
            Debug.Log($"[CraftingMaterialManager] Set {materialData.materialName} from {currentAmount} to {amount}");
        }
        
        OnMaterialInventoryChanged?.Invoke();
    }
    
    private int GetMaterialCountInternal(CraftingMaterialData materialData)
    {
        if (materialData == null) return 0;
        
        return materialInventory
            .Where(mat => mat.materialDataTemplate == materialData)
            .Sum(mat => mat.stackCount);
    }
    
    private void CleanupInventory()
    {
        // Remove empty stacks
        materialInventory.RemoveAll(mat => mat.stackCount <= 0 || mat.materialDataTemplate == null);
        
        // Validate all remaining stacks
        foreach (var material in materialInventory)
        {
            material.ValidateAndFix();
        }
    }
    
    #endregion
    
    #region Persistence
    
    private void LoadMaterials()
    {
        // For now, start with empty inventory - implement proper save/load later
        materialInventory.Clear();
        
        if (enableDebugLogging)
        {
            Debug.Log("[CraftingMaterialManager] Materials loaded (empty start)");
        }
        
        OnMaterialInventoryChanged?.Invoke();
    }
    
    private void SaveMaterials()
    {
        // TODO: Implement proper save/load system for materials
        // For now, just log the current state
        
        if (enableDebugLogging)
        {
            var summary = GetMaterialSummary();
            Debug.Log($"[CraftingMaterialManager] Saving {summary.Count} material types with {materialInventory.Count} total stacks");
        }
    }
    
    #endregion
    
    #region Debug Tools
    
    [Title("Debug Tools")]
    [Button("Add Random Material x10")]
    [PropertyOrder(20)]
    private void DebugAddRandomMaterials()
    {
        // Find all CraftingMaterialData assets in the project
        var allMaterials = Resources.FindObjectsOfTypeAll<CraftingMaterialData>();
        
        if (allMaterials.Length == 0)
        {
            Debug.LogWarning("[CraftingMaterialManager] No CraftingMaterialData assets found! Create some materials first.");
            return;
        }
        
        // Add 10 random materials
        for (int i = 0; i < 10; i++)
        {
            var randomMaterial = allMaterials[UnityEngine.Random.Range(0, allMaterials.Length)];
            AddMaterial(randomMaterial, UnityEngine.Random.Range(1, 6)); // 1-5 of each
        }
        
        Debug.Log($"[CraftingMaterialManager] Added 10 random materials from {allMaterials.Length} available types");
    }
    
    [Button("Add Specific Material")]
    [PropertyOrder(20)]
    public CraftingMaterialData specificMaterial;
    
    [Button("Add 5 of Specific Material")]
    [PropertyOrder(20)]
    private void DebugAddSpecificMaterial()
    {
        if (specificMaterial == null)
        {
            Debug.LogWarning("[CraftingMaterialManager] Please assign a CraftingMaterialData to 'specificMaterial' field first!");
            return;
        }
        
        AddMaterial(specificMaterial, 5);
        Debug.Log($"[CraftingMaterialManager] Added 5x {specificMaterial.materialName}");
    }
    
    [Button("Clear All Materials")]
    [PropertyOrder(21)]
    private void DebugClearAll()
    {
        materialInventory.Clear();
        OnMaterialInventoryChanged?.Invoke();
        Debug.Log("[CraftingMaterialManager] All materials cleared");
    }
    
    [Button("Log Material Summary")]
    [PropertyOrder(22)]
    private void DebugLogSummary()
    {
        var summary = GetMaterialSummary();
        Debug.Log($"[CraftingMaterialManager] Material Summary: {summary.Count} types, {materialInventory.Count} stacks");
        
        foreach (var kvp in summary)
        {
            Debug.Log($"  {kvp.Key.materialName}: {kvp.Value}");
        }
    }
    
    [Button("Save Materials Now")]
    [PropertyOrder(23)]
    private void DebugSave()
    {
        SaveMaterials();
    }
    
    #endregion
}