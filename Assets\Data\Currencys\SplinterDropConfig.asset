%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9240499747eff5343b0e30b80165889e, type: 3}
  m_Name: SplinterDropConfig
  m_EditorClassIdentifier: Assembly-CSharp::SplinterDropConfig
  baseDropChance: 0.75
  distanceScalingFactor: 0.1
  enemyConfigurations:
  - enemyType: 7
    overrideDropChance: -1
    splinterDrops:
    - splinterType: 32
      prefab: {fileID: 9207650179189352675, guid: bb7941bc6f767b24e95c6b2712932c1d, type: 3}
      weight: 1
      minAmount: 1
      maxAmount: 3
  - enemyType: 2
    overrideDropChance: -1
    splinterDrops:
    - splinterType: 1
      prefab: {fileID: 9207650179189352675, guid: b98987b37d3c0604d9613850b7dc909b, type: 3}
      weight: 1
      minAmount: 1
      maxAmount: 3
  - enemyType: 3
    overrideDropChance: -1
    splinterDrops:
    - splinterType: 2
      prefab: {fileID: 9207650179189352675, guid: dd99a1b702424fd4ba9b048080da8955, type: 3}
      weight: 1
      minAmount: 1
      maxAmount: 3
  - enemyType: 5
    overrideDropChance: -1
    splinterDrops:
    - splinterType: 8
      prefab: {fileID: 9207650179189352675, guid: 47f7e78c21279f04f9eb38ac04c38a66, type: 3}
      weight: 1
      minAmount: 1
      maxAmount: 3
  - enemyType: 4
    overrideDropChance: -1
    splinterDrops:
    - splinterType: 4
      prefab: {fileID: 0}
      weight: 1
      minAmount: 1
      maxAmount: 3
