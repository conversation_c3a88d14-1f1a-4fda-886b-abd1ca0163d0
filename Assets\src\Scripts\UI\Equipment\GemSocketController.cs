using System.Collections.Generic;
using UnityEngine;

public class GemSocketController
{
    public GemInstance skillGemInstance;
    public List<GemInstance> supportGemInstances = new List<GemInstance>();
    
    #region Tag Compatibility
    
    /// <summary>
    /// Checks if a support gem is compatible with the current skill gem
    /// </summary>
    public bool IsCompatible(GemInstance supportGem)
    {
        if (skillGemInstance == null || supportGem == null)
            return false;
            
        var skillData = skillGemInstance.gemDataTemplate as SkillGemData;
        var supportData = supportGem.gemDataTemplate as SupportGemData;
        
        if (skillData == null || supportData == null)
            return false;
            
        // Check if there's any tag overlap between skill gem tags and support gem compatible tags
        return (skillData.gemTags & supportData.compatibleTags) != GemTag.None;
    }
    
    /// <summary>
    /// Gets only the compatible support gems for calculations
    /// </summary>
    public List<GemInstance> GetCompatibleSupportGems()
    {
        var compatibleGems = new List<GemInstance>();
        
        foreach (var supportGem in supportGemInstances)
        {
            if (IsCompatible(supportGem))
            {
                compatibleGems.Add(supportGem);
            }
        }
        
        return compatibleGems;
    }
    
    #endregion
    
    // Legacy support
    public SkillGemData skillGem => skillGemInstance?.gemDataTemplate as SkillGemData;
    public List<GemData> supportGems 
    {
        get
        {
            var gems = new List<GemData>();
            foreach (var instance in supportGemInstances)
            {
                if (instance?.gemDataTemplate != null)
                    gems.Add(instance.gemDataTemplate);
            }
            return gems;
        }
    }
    
    public float CalculateFinalDamage()
    {
        if (skillGemInstance == null || !skillGemInstance.IsSkillGem)
            return 0f;
        
        // Start with skill gem's damage (including level/quality bonuses)
        float baseDamage = skillGemInstance.GetSkillDamage();
        
        // Collect modifiers from support gems
        float flatDamageBonus = 0f;
        float damageMore = 1f; // Multiplicative "more" modifiers only
        
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance != null && supportInstance.IsSupportGem)
            {
                // Apply "more" multipliers (multiplicative) - includes base + random modifiers
                damageMore *= supportInstance.GetSupportDamageMultiplier();

                // Flat damage from random modifiers
                flatDamageBonus += supportInstance.GetSupportFlatDamageBonus();
            }
        }
        
        // Apply damage formula: (base + flat) * more multipliers
        // Note: "increased" modifiers are handled separately via GetTotalIncreasedDamage()
        float damage = (baseDamage + flatDamageBonus) * damageMore;
        
        return damage;
    }
    
    public float GetTotalFlatDamage()
    {
        float flatDamageBonus = 0f;
        
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance != null && supportInstance.IsSupportGem)
            {
                // Flat damage from random modifiers
                flatDamageBonus += supportInstance.GetSupportFlatDamageBonus();
            }
        }
        
        return flatDamageBonus;
    }
    
    /// <summary>
    /// Gets the total "increased damage" from all linked support gems.
    /// This should be added additively with player's increased damage.
    /// </summary>
    public float GetTotalIncreasedDamage()
    {
        float totalIncreased = 0f;
        
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance != null && supportInstance.IsSupportGem)
            {
                // Use the gem instance method which handles base + random modifiers
                totalIncreased += supportInstance.GetSupportIncreasedDamage();
            }
        }
        
        return totalIncreased;
    }
    
    public float CalculateFinalCooldown()
    {
        if (skillGemInstance == null || !skillGemInstance.IsSkillGem)
            return 1f;
        
        // Start with skill gem's cooldown (including level reduction)
        float cooldown = skillGemInstance.GetSkillCooldown();
        
        // Apply support gem modifiers
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance != null && supportInstance.IsSupportGem)
            {
                cooldown *= supportInstance.GetSupportCooldownMultiplier();
            }
        }
        
        return cooldown;
    }
    
    public float CalculateFinalManaCost()
    {
        if (skillGemInstance == null || !skillGemInstance.IsSkillGem)
            return 0f;
        
        // Start with skill gem's mana cost (including level increase)
        float manaCost = skillGemInstance.GetSkillManaCost();
        
        // Apply support gem modifiers (including random modifiers)
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance != null)
            {
                manaCost *= supportInstance.GetSupportManaCostMultiplier();
            }
        }
        
        return manaCost;
    }
    
    public bool HasPierce()
    {
        // Check for intrinsic pierce from skill gem first
        if (skillGemInstance?.gemDataTemplate is SkillGemData skillData && skillData.intrinsicHasPierce)
        {
            return true;
        }
        
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance?.gemDataTemplate is SupportGemData support && support.addsPierce)
                return true;
            // Check for pierce from random modifiers
            if (supportInstance != null && supportInstance.GetRandomModifierBonus(SupportGemModifierType.ProjectilePierce) > 0)
                return true;
        }
        return false;
    }
    
    public bool HasChain()
    {
        // Check for intrinsic chain from skill gem first
        if (skillGemInstance?.gemDataTemplate is SkillGemData skillData && skillData.intrinsicHasChain)
        {
            return true;
        }
        
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance?.gemDataTemplate is SupportGemData support && support.addsChain)
                return true;
        }
        return false;
    }
    
    public int GetChainCount()
    {
        int maxChains = 0;
        
        // Check for intrinsic chain count from skill gem first
        if (skillGemInstance?.gemDataTemplate is SkillGemData skillData && skillData.intrinsicHasChain)
        {
            maxChains = skillData.intrinsicChainCount;
        }
        
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance != null)
            {
                int instanceChains = supportInstance.GetChainCount();
                maxChains = Mathf.Max(maxChains, instanceChains);
                
                // Add chain count from random modifiers
                int modifierChains = Mathf.RoundToInt(supportInstance.GetRandomModifierBonus(SupportGemModifierType.ChainCount));
                maxChains = Mathf.Max(maxChains, modifierChains);
            }
        }
        return maxChains;
    }
    
    public bool HasFork()
    {
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance?.gemDataTemplate is SupportGemData support && support.addsFork)
                return true;
        }
        return false;
    }
    
    public int GetForkCount()
    {
        int maxForks = 0;
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance != null)
            {
                int instanceForks = supportInstance.GetForkCount();
                maxForks = Mathf.Max(maxForks, instanceForks);
            }
        }
        return maxForks;
    }
    
    public float GetForkAngle()
    {
        float maxAngle = 30f; // Default fork angle
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance != null)
            {
                float instanceAngle = supportInstance.GetForkAngle();
                if (instanceAngle > maxAngle)
                    maxAngle = instanceAngle;
            }
        }
        return maxAngle;
    }
    
    public bool HasAreaDamage()
    {
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance?.gemDataTemplate is SupportGemData support && support.addsAreaDamage)
                return true;
        }
        return false;
    }

    public bool HasSpellEcho()
    {
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance?.gemDataTemplate is SupportGemData support && support.addsSpellEcho)
                return true;
        }
        return false;
    }

    public bool HasMultipleProjectiles()
    {
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance?.gemDataTemplate is SupportGemData support && support.addsMultipleProjectiles)
                return true;
        }
        return false;
    }
    
    public float GetAreaRadius()
    {
        // Only calculate area radius if skill has area damage support gems
        if (!HasAreaDamage())
            return 0f;
        
        // Start with base area radius for area effects (typical AOE skill base radius)
        float baseRadius = 2.0f;
        
        // Apply area modifiers from support gems
        float totalAreaIncreased = 0f;
        float totalAreaMore = 1f;
        
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance != null)
            {
                // Accumulate increased area modifiers (additive)
                totalAreaIncreased += supportInstance.GetSupportAreaIncreased();
                
                // Accumulate more area modifiers (multiplicative)
                totalAreaMore *= supportInstance.GetSupportAreaMore();
            }
        }
        
        // Apply Path of Exile area scaling formula: base * (1 + increased/100) * more
        float finalRadius = baseRadius * (1f + totalAreaIncreased / 100f) * totalAreaMore;
        
        return finalRadius;
    }
    
    public int GetTotalProjectileCount()
    {
        // Start with intrinsic projectiles from skill gem itself
        int totalProjectiles = 1; // Default
        if (skillGemInstance?.gemDataTemplate is SkillGemData skillData)
        {
            totalProjectiles = skillData.intrinsicProjectileCount;
        }
        
        // Add extra projectiles from support gems (including random modifiers)
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance != null)
            {
                totalProjectiles += supportInstance.GetExtraProjectiles();
            }
        }
        
        return totalProjectiles;
    }
    
    public float GetProjectileSpreadAngle()
    {
        float maxSpread = 0f;
        
        // Check for intrinsic spread angle from skill gem first
        if (skillGemInstance?.gemDataTemplate is SkillGemData skillData && 
            skillData.intrinsicProjectileCount > 1)
        {
            maxSpread = skillData.intrinsicSpreadAngle;
        }
        
        // Get the largest spread angle from support gems
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance?.gemDataTemplate is SupportGemData support && support.addsMultipleProjectiles)
            {
                if (support.projectileSpreadAngle > maxSpread)
                    maxSpread = support.projectileSpreadAngle;
            }
        }
        
        return maxSpread;
    }
    
    public bool UseParallelProjectiles()
    {
        // Check if any support gem enables parallel projectiles
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance?.gemDataTemplate is SupportGemData support && 
                support.addsMultipleProjectiles && support.useParallelProjectiles)
            {
                return true;
            }
        }
        return false;
    }
    
    public bool UseRandomSpread()
    {
        // Check for intrinsic random spread from skill gem first
        if (skillGemInstance?.gemDataTemplate is SkillGemData skillData && 
            skillData.intrinsicProjectileCount > 1 && skillData.intrinsicUseRandomSpread)
        {
            return true;
        }
        
        // Check if any support gem enables random spread
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance?.gemDataTemplate is SupportGemData support && 
                support.addsMultipleProjectiles && support.useRandomSpread)
            {
                return true;
            }
        }
        return false;
    }
    
    public float GetProjectileLateralOffset()
    {
        float maxOffset = 0.6f; // Default offset
        
        // Get the lateral offset from support gems that use parallel projectiles
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance?.gemDataTemplate is SupportGemData support && 
                support.addsMultipleProjectiles && support.useParallelProjectiles)
            {
                if (support.projectileLateralOffset > maxOffset)
                    maxOffset = support.projectileLateralOffset;
            }
        }
        
        return maxOffset;
    }
    
    public float CalculateFinalAttackSpeed()
    {
        if (skillGemInstance == null || !skillGemInstance.IsSkillGem)
            return 1f;
            
        var skillData = skillGemInstance.gemDataTemplate as SkillGemData;
        if (skillData == null)
            return 1f;
            
        // Start with skill gem's attack speed
        float attackSpeed = skillData.attackSpeedMultiplier;
        
        // Apply support gem modifiers (including random modifiers)
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance != null)
            {
                attackSpeed *= supportInstance.GetSupportAttackSpeedMultiplier();
            }
        }
        
        return attackSpeed;
    }
    
    public float CalculateFinalCritChance()
    {
        if (skillGemInstance == null || !skillGemInstance.IsSkillGem)
            return 0f;
            
        var skillData = skillGemInstance.gemDataTemplate as SkillGemData;
        if (skillData == null)
            return 0f;
            
        // Start with skill gem's crit chance
        float critChance = skillData.critChance;
        
        // Add crit chance from support gems (including random modifiers)
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance != null)
            {
                critChance += supportInstance.GetSupportCritChanceBonus();
            }
        }
        
        return Mathf.Clamp(critChance, 0f, 100f);
    }
    
    public float CalculateFinalCritMultiplier()
    {
        if (skillGemInstance == null || !skillGemInstance.IsSkillGem)
            return 1f;
            
        var skillData = skillGemInstance.gemDataTemplate as SkillGemData;
        if (skillData == null)
            return 1f;
            
        // Start with skill gem's crit multiplier
        float critMultiplier = skillData.critMultiplier;
        
        // Apply support gem modifiers (including random modifiers)
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance != null)
            {
                critMultiplier *= supportInstance.GetSupportCritMultiplierModifier();
            }
        }
        
        return critMultiplier;
    }
    
    public float CalculateFinalDuration()
    {
        if (skillGemInstance == null || !skillGemInstance.IsSkillGem)
            return 0f;
            
        var skillData = skillGemInstance.gemDataTemplate as SkillGemData;
        if (skillData == null)
            return 0f;
            
        // Start with skill gem's base duration
        float duration = skillData.duration;
        
        // Apply support gem modifiers (including random modifiers)
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance != null)
            {
                duration *= supportInstance.GetSupportSkillDurationMultiplier();
            }
        }
        
        return duration;
    }

    /// <summary>
    /// Check if this skill has autonomous support gems
    /// </summary>
    public bool HasAutonomous()
    {
        // Respect tag compatibility - only check compatible support gems
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance?.gemDataTemplate is SupportGemData supportGem && supportGem.addsAutonomous)
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// Get the autonomous range from support gems
    /// </summary>
    public float GetAutonomousRange()
    {
        // Respect tag compatibility - only check compatible support gems
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance?.gemDataTemplate is SupportGemData supportGem && supportGem.addsAutonomous)
            {
                return supportGem.autonomousRange;
            }
        }

        return 0f;
    }

    /// <summary>
    /// Get the autonomous update interval from support gems
    /// </summary>
    public float GetAutonomousUpdateInterval()
    {
        // Respect tag compatibility - only check compatible support gems
        foreach (var supportInstance in GetCompatibleSupportGems())
        {
            if (supportInstance?.gemDataTemplate is SupportGemData supportGem && supportGem.addsAutonomous)
            {
                return supportGem.autonomousUpdateInterval;
            }
        }

        return 0.5f; // Default interval
    }
}