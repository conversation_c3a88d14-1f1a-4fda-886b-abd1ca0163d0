using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// AOE projectile that deals both direct impact damage and area-of-effect damage.
/// Inherits all standard projectile functionality and adds explosion mechanics.
/// Maintains zero-GC performance with pre-allocated arrays and object pooling integration.
/// </summary>
[RequireComponent(typeof(Collider2D))]
public class AOEProjectile : Projectile
{
    /// <summary>
    /// AOE behavior mode enumeration
    /// </summary>
    public enum AOEMode 
    { 
        Explosion,      // Traditional AOE explosion damage
        InstantSpells   // Spawn instant spells on each detected enemy
    }
    [Title("AOE Configuration")]
    [SerializeField, Tooltip("AOE behavior mode - Explosion or InstantSpells")]
    private AOEMode aoeMode = AOEMode.Explosion;
    
    [SerializeField, Tooltip("Trigger AOE explosion on direct hit")]
    private bool triggerAOEOnDirectHit = true;
    
    [SerializeField, Tooltip("Trigger AOE explosion when lifetime expires")]
    private bool triggerAOEOnLifetimeEnd = false;
    
    [Ser<PERSON><PERSON><PERSON>ield, Range(0.1f, 15f), <PERSON><PERSON><PERSON>("Base AOE radius before support gem modifiers")]
    private float aoeRadius = 0.19f;
    
    [ShowIf("aoeMode", AOEMode.Explosion)]
    [SerializeField, Range(0.1f, 2f), Tooltip("Damage multiplier for AOE damage (e.g., 0.7 = 70% of direct damage)")]
    private float aoeDamageMultiplier = 0.7f;
    
    [ShowIf("aoeMode", AOEMode.InstantSpells)]
    [SerializeField, Tooltip("Instant spell prefab to spawn on each detected enemy (must have InstantSpell component)")]
    private GameObject instantSpellPrefab;
    
    [Title("AOE Visual Effects")]
    [ShowIf("aoeMode", AOEMode.Explosion)]
    [SerializeField, Tooltip("Particle effect type for explosion")]
    private ParticleType explosionParticleType = ParticleType.SmokeImpact;
    
    [ShowIf("aoeMode", AOEMode.Explosion)]
    [SerializeField, Range(5, 100), Tooltip("Number of explosion particles")]
    private int explosionParticleCount = 25;
    
    [ShowIf("aoeMode", AOEMode.Explosion)]
    [SerializeField, Tooltip("Optional warning indicator prefab for delayed explosions")]
    private GameObject aoeWarningPrefab;
    
    [ShowIf("aoeMode", AOEMode.Explosion)]
    [SerializeField, Tooltip("AOE explosion prefab with animation (must have AOEExplosion component)")]
    private GameObject aoeExplosionPrefab;
    
    [Title("Debug")]
    [SerializeField, Tooltip("Show AOE explosion debug info")]
    private bool showAOEDebug = false;
    
    // Pre-allocated arrays for zero-GC collision detection
    private static readonly Collider2D[] aoeResults = new Collider2D[64];
    
    // Store original values for pool reset
    private float originalAoeRadius;
    private float originalAoeDamageMultiplier;
    
    // AOE explosion state
    private bool hasExploded = false;
    
    protected override void Awake()
    {
        base.Awake();
        
        // Store original values for pool reset
        originalAoeRadius = aoeRadius;
        originalAoeDamageMultiplier = aoeDamageMultiplier;
    }
    
    protected override void Update()
    {
        // Override base Update to inject AOE explosion logic during lifetime expiration
        // Cannot call base.Update() because AOE explosion must occur after movement/collision
        // but before deactivation - requires custom sequence control
        if (!_isActive || hasBeenHit) return;
        
        // Move projectile
        Vector2 movement = _direction * speed * Time.deltaTime;
        
        // Check for collisions using CircleCast for moving objects
        CheckCollisionsDuringMovement(movement);
        
        // Apply movement after collision check
        transform.position += (Vector3)movement;
        _timeAlive += Time.deltaTime;
        
        // Check if exceeded lifetime
        if (_timeAlive >= lifetime)
        {
            // Trigger AOE explosion on lifetime end if configured
            if (!hasExploded && triggerAOEOnLifetimeEnd)
            {
                TriggerAOEExplosion(transform.position);
            }
            
            SpawnDespawnParticles();
            Deactivate();
        }
    }
    
    protected override void ProcessHit(RaycastHit2D hit)
    {
        if (hit.collider == null) return;
        
        GameObject target = hit.collider.gameObject;
        int targetID = target.GetInstanceID();
        
        // Skip if already hit this target
        if (hitTargetIDs.Contains(targetID)) return;
        
        // Check if target is on a layer we can damage
        int targetLayer = target.layer;
        bool canDamage = CanDamageLayer(targetLayer);
        
        Vector3 impactPoint = hit.point;
        
        if (canDamage && ApplyDamage(target))
        {
            hitTargetIDs.Add(targetID);
            cachedTarget = target.transform;
            
            // Spawn impact particles
            SpawnImpactParticles(hit.point);
            
            // Trigger AOE explosion if configured and not already exploded
            if (!hasExploded && triggerAOEOnDirectHit)
            {
                TriggerAOEExplosion(impactPoint);
            }
            
            // Deactivate after successful hit (AOE projectiles don't chain/pierce by default)
            hasBeenHit = true;
            Deactivate();
            return;
        }
        
        // Check for wall/environment collision
        if (GameLayers.IsWall(targetLayer) || GameLayers.IsEnvironment(targetLayer))
        {
            // Spawn impact particles for wall hits
            SpawnImpactParticles(hit.point);
            
            // Trigger AOE explosion on wall hit if configured
            if (!hasExploded && triggerAOEOnDirectHit)
            {
                TriggerAOEExplosion(impactPoint);
            }
            
            hasBeenHit = true;
            Deactivate();
        }
    }
    
    /// <summary>
    /// Trigger AOE explosion at specified center point
    /// </summary>
    private void TriggerAOEExplosion(Vector3 center)
    {
        if (hasExploded) return; // Prevent multiple explosions
        
        hasExploded = true;
        
        if (showAOEDebug)
        {
            Debug.Log($"[AOEProjectile] Triggering {aoeMode} at {center} with radius {GetEffectiveAOERadius()}");
        }
        
        // Apply AOE effect based on selected mode
        switch (aoeMode)
        {
            case AOEMode.Explosion:
                ApplyAOEDamage(center);
                break;
            case AOEMode.InstantSpells:
                SpawnInstantSpells(center);
                break;
        }
        
        // Spawn explosion visual effects
        SpawnExplosionEffects(center);
        
        // Apply blood splatters for visual feedback
        ApplyBloodEffects(center);
    }
    
    /// <summary>
    /// Apply AOE damage using pooled AOEExplosion with immediate damage and visual animation
    /// </summary>
    private void ApplyAOEDamage(Vector3 center)
    {
        float effectiveRadius = GetEffectiveAOERadius();
        LayerMask targetMask = GetTargetLayerMask(_currentLayer);
        
        if (showAOEDebug)
        {
            Debug.Log($"[AOEProjectile] Spawning AOE explosion at {center} with radius {effectiveRadius}");
        }
        
        // Check if we have the explosion prefab
        if (aoeExplosionPrefab == null)
        {
            Debug.LogError("[AOEProjectile] No AOE explosion prefab assigned! AOE damage will not be applied. Please assign an explosion prefab in the inspector.");
            return;
        }
        
        // Spawn pooled explosion GameObject
        GameObject explosionObj = null;
        if (PoolManager.Instance != null)
        {
            explosionObj = PoolManager.Instance.Spawn(aoeExplosionPrefab, center, Quaternion.identity);
        }
    
        
        // Set explosion to same layer as parent projectile for consistent collision logic
        if (explosionObj != null)
        {
            explosionObj.layer = _currentLayer;
        }
        
        if (explosionObj == null)
        {
            Debug.LogError("[AOEProjectile] Failed to spawn AOE explosion! Pool may be exhausted or prefab is invalid.");
            return;
        }
        
        
        if (PoolManager.Instance.GetCachedComponent<AOEExplosion>(explosionObj, out var explosionComponent))
        {
            ConfigureExplosion(explosionComponent, center, effectiveRadius, targetMask);
        }
        else
        {
            Debug.LogError("[AOEProjectile] AOE explosion prefab missing AOEExplosion component! Please add AOEExplosion script to the prefab.");
            PoolManager.Instance.Despawn(explosionObj);
        }
    }
    
    /// <summary>
    /// Configure the spawned AOEExplosion with all necessary parameters
    /// </summary>
    private void ConfigureExplosion(AOEExplosion explosion, Vector3 center, float effectiveRadius, LayerMask targetMask)
    {
        // Calculate area modifiers from support gems
        float totalAreaIncreased = 0f;
        float totalAreaMore = 1f;

        if (supportGems != null)
        {
            foreach (var gem in supportGems)
            {
                var supportGemData = gem.gemDataTemplate as SupportGemData;
                if (supportGemData != null && supportGemData.addsAreaDamage)
                {
                    // Accumulate increased modifiers (additive)
                    float areaIncreased = gem.GetSupportAreaIncreased();
                    totalAreaIncreased += areaIncreased;

                    // Accumulate more multipliers (multiplicative)
                    float areaMore = gem.GetSupportAreaMore();
                    totalAreaMore *= areaMore;

                    if (showAOEDebug)
                    {
                        Debug.Log($"[AOEProjectile] {supportGemData.gemName} adds {areaIncreased:+0;-0}% increased, {(areaMore - 1f) * 100:+0;-0}% more area");
                    }
                }
            }
        }
        
        // Prepare damage breakdown if available
        DamageBreakdown? scaledBreakdown = null;
        if (damageBreakdown.HasValue && damageBreakdown.Value.TotalDamage > 0f)
        {
            var breakdown = new DamageBreakdown(damageBreakdown.Value);
            breakdown.ScaleAllDamage(aoeDamageMultiplier);
            scaledBreakdown = breakdown;
        }
        
        // Configure explosion parameters - pass modifiers instead of final radius
        explosion.SetupExplosion(
            center: center,
            targets: targetMask,
            aoeDamageMultiplier: aoeDamageMultiplier,
            radiusIncreased: totalAreaIncreased,
            radiusMore: totalAreaMore,
            breakdown: scaledBreakdown,
            skillGem: skillGemData,
            supportGemList: supportGems,
            critical: _currentCritResult && _hasRolledCrit,
            critMult: critMultiplier,
            ailment: ailmentChance,
            damage: damage,
            type: damageType
        );
        
        if (showAOEDebug)
        {
            Debug.Log($"[AOEProjectile] Configured explosion with {totalAreaIncreased:+0;-0}% increased area, {totalAreaMore:F2}x more area");
        }
    }
    
    /// <summary>
    /// Spawn instant spells on all detected enemies in AOE radius
    /// </summary>
    private void SpawnInstantSpells(Vector3 center)
    {
        float effectiveRadius = GetEffectiveAOERadius();
        LayerMask targetMask = GetTargetLayerMask(_currentLayer);
        
        if (showAOEDebug)
        {
            Debug.Log($"[AOEProjectile] Spawning instant spells at {center} with radius {effectiveRadius}");
        }
        
        // Check if we have the instant spell prefab
        if (instantSpellPrefab == null)
        {
            Debug.LogError("[AOEProjectile] No instant spell prefab assigned! Instant spells will not be spawned. Please assign a spell prefab in the inspector.");
            return;
        }
        
        // Find all targets in explosion radius
        var contactFilter = new ContactFilter2D();
        contactFilter.SetLayerMask(targetMask);
        contactFilter.useTriggers = true;
        int hits = Physics2D.OverlapCircle(center, effectiveRadius, contactFilter, aoeResults);
        
        if (showAOEDebug)
        {
            Debug.Log($"[AOEProjectile] Found {hits} targets for instant spells");
        }
        
        // Spawn instant spell for each target
        for (int i = 0; i < hits; i++)
        {
            var target = aoeResults[i].gameObject;
            if (target == gameObject) continue; // Skip self
            
            SpawnInstantSpellOnTarget(target);
        }
    }
    
    /// <summary>
    /// Spawn an instant spell on a specific target
    /// </summary>
    private void SpawnInstantSpellOnTarget(GameObject target)
    {
        // Spawn pooled instant spell GameObject at target position
        GameObject spellObj = null;
        if (PoolManager.Instance != null)
        {
            spellObj = PoolManager.Instance.Spawn(instantSpellPrefab, target.transform.position, Quaternion.identity);
        }
        
        if (spellObj == null)
        {
            Debug.LogError("[AOEProjectile] Failed to spawn instant spell! Pool may be exhausted or prefab is invalid.");
            return;
        }
        
        // Set spell to same layer as parent projectile for consistent collision logic
        spellObj.layer = _currentLayer;
        
        // Configure the instant spell with target and damage data
        if (PoolManager.Instance.GetCachedComponent<InstantSpell>(spellObj, out var spellComponent))
        {
            ConfigureInstantSpell(spellComponent, target.transform);
        }
        else
        {
            Debug.LogError("[AOEProjectile] Instant spell prefab missing InstantSpell component! Please add InstantSpell script to the prefab.");
            PoolManager.Instance.Despawn(spellObj);
        }
    }
    
    /// <summary>
    /// Configure the spawned instant spell with all necessary parameters
    /// </summary>
    private void ConfigureInstantSpell(InstantSpell spell, Transform target)
    {
        // Calculate final damage for InstantSpell
        float finalDamage = damage;
        
        // Apply critical hit if rolled
        if (_currentCritResult && _hasRolledCrit)
        {
            finalDamage *= critMultiplier;
        }
        
        // Set spell properties BEFORE Initialize (prevents damage breakdown error)
        spell.critChance = critChance;
        spell.critMultiplier = critMultiplier;
        spell.damageType = damageType;
        spell.ailmentChance = ailmentChance;
        spell.skillGemData = skillGemData;
        spell.supportGems = supportGems;
        
        // Set damage breakdown if available BEFORE Initialize
        if (damageBreakdown.HasValue && damageBreakdown.Value.TotalDamage > 0f)
        {
            var spellBreakdown = new DamageBreakdown(damageBreakdown.Value);
            
            // Apply critical hit scaling to breakdown
            if (_currentCritResult && _hasRolledCrit)
            {
                spellBreakdown.ScaleAllDamage(critMultiplier);
            }
            
            spell.damageBreakdown = spellBreakdown;
        }
        
        // Initialize the instant spell at target position (this triggers ApplyOverlapDamage)
        spell.Initialize(target.position, finalDamage, _currentLayer);
        
        if (showAOEDebug)
        {
            Debug.Log($"[AOEProjectile] Configured instant spell on {target.name} with {finalDamage} damage at {target.position}");
        }
    }
    
    
    /// <summary>
    /// Get effective AOE radius with support gem modifiers applied
    /// </summary>
    private float GetEffectiveAOERadius()
    {
        float baseRadius = aoeRadius;

        // Calculate total area increased percentage and more multipliers from support gems
        float totalAreaIncreased = 0f;
        float totalAreaMore = 1f;

        if (supportGems != null)
        {
            foreach (var gem in supportGems)
            {
                var supportGemData = gem.gemDataTemplate as SupportGemData;
                if (supportGemData != null && supportGemData.addsAreaDamage)
                {
                    // Accumulate increased modifiers (additive)
                    float areaIncreased = gem.GetSupportAreaIncreased();
                    totalAreaIncreased += areaIncreased;

                    // Accumulate more multipliers (multiplicative)
                    float areaMore = gem.GetSupportAreaMore();
                    totalAreaMore *= areaMore;

                    if (showAOEDebug)
                    {
                        Debug.Log($"[AOEProjectile] {supportGemData.gemName} adds {areaIncreased:+0;-0}% increased, {(areaMore - 1f) * 100:+0;-0}% more area");
                    }
                }
            }
        }

        // Apply modifiers in correct order: base * (1 + increased%) * more_multipliers
        float increasedMultiplier = 1f + totalAreaIncreased / 100f;
        float effectiveRadius = baseRadius * increasedMultiplier * totalAreaMore;

        if (showAOEDebug)
        {
            Debug.Log($"[AOEProjectile] Area calculation: base={baseRadius:F1}, increased={totalAreaIncreased:+0;-0}%, more={totalAreaMore:F2}x, effective={effectiveRadius:F1}");
        }

        // Ensure minimum radius
        return Mathf.Max(effectiveRadius, 1f);
    }
    
    /// <summary>
    /// Spawn explosion particle effects
    /// </summary>
    private void SpawnExplosionEffects(Vector3 center)
    {
        if (ParticleEffectManager.Instance == null) return;
        
        // Main explosion particles
        ParticleEffectManager.Instance.SpawnParticle(
            explosionParticleType, 
            center, 
            explosionParticleCount
        );
        
        // Scale particle count based on AOE radius for visual feedback
        float radiusScale = GetEffectiveAOERadius() / aoeRadius;
        int scaledParticleCount = Mathf.RoundToInt(explosionParticleCount * radiusScale);
        
        // Additional ring particles for area indication
        SpawnRingParticles(center, GetEffectiveAOERadius(), Mathf.Min(scaledParticleCount, 50));
    }
    
    /// <summary>
    /// Spawn ring particles around explosion perimeter
    /// </summary>
    private void SpawnRingParticles(Vector3 center, float radius, int count)
    {
        count = Mathf.Clamp(count, 8, 32); // Reasonable range for performance
        
        for (int i = 0; i < count; i++)
        {
            float angle = (i / (float)count) * 2f * Mathf.PI;
            Vector3 ringPosition = center + new Vector3(
                Mathf.Cos(angle) * radius * 0.8f,
                Mathf.Sin(angle) * radius * 0.8f,
                0
            );
            
            ParticleEffectManager.Instance?.SpawnParticle(
                ParticleType.SparkImpact,
                ringPosition,
                2
            );
        }
    }
    
    /// <summary>
    /// Apply blood splatter effects for damaged enemies
    /// </summary>
    private void ApplyBloodEffects(Vector3 center)
    {
        // Only apply blood effects if we have enemies in range
        LayerMask enemyMask = GameLayers.EnemyMask;
        int hits = Physics2D.OverlapCircleNonAlloc(
            center, 
            GetEffectiveAOERadius(), 
            aoeResults, 
            enemyMask
        );
        
        // Spawn blood splatters for each enemy hit
        for (int i = 0; i < hits; i++)
        {
            if (aoeResults[i] == null) continue;
            
            Vector3 enemyPosition = aoeResults[i].transform.position;
            Vector3 direction = (enemyPosition - center).normalized;
            
            // Check if BloodSplatterManager exists and spawn hit splatter
            if (BloodSystem.BloodSplatterManager.Instance != null)
            {
                BloodSystem.BloodSplatterManager.Instance.SpawnHitSplatter(
                    enemyPosition,
                    direction * 2f // Velocity away from explosion center
                );
            }
        }
    }
    
    // ISpawnable implementation - extend base class behavior
    public override void OnSpawn()
    {
        base.OnSpawn();
        hasExploded = false;
    }
    
    public override void OnDespawn()
    {
        base.OnDespawn();
        
        // AOE-specific cleanup
        hasExploded = false;
        
        // Clear AOE results array
        System.Array.Clear(aoeResults, 0, aoeResults.Length);
        
        // Reset AOE configuration to defaults
        aoeRadius = originalAoeRadius;
        aoeDamageMultiplier = originalAoeDamageMultiplier;
        
        // Reset instant spell prefab reference (but keep the assigned prefab)
        // Note: instantSpellPrefab itself should not be reset as it's an inspector-assigned reference
    }
    
    /// <summary>
    /// Set AOE-specific parameters (used by skill executors)
    /// </summary>
    public void SetAOEParameters(float radius, float damageMultiplier, bool triggerOnHit = true, bool triggerOnTimeout = false)
    {
        aoeRadius = radius;
        aoeDamageMultiplier = damageMultiplier;
        triggerAOEOnDirectHit = triggerOnHit;
        triggerAOEOnLifetimeEnd = triggerOnTimeout;
    }
    
    /// <summary>
    /// Debug visualization for AOE radius
    /// </summary>
    void OnDrawGizmosSelected()
    {
        if (!showAOEDebug) return;
        
        // Draw AOE radius - uniform damage across entire radius
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, GetEffectiveAOERadius());
        
        // AOE trigger mode indicators
        if (triggerAOEOnDirectHit)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawWireCube(transform.position + Vector3.up * (GetEffectiveAOERadius() + 0.8f), Vector3.one * 0.3f);
        }
        
        if (triggerAOEOnLifetimeEnd)
        {
            Gizmos.color = Color.blue;
            Gizmos.DrawWireCube(transform.position + Vector3.up * (GetEffectiveAOERadius() + 1.2f), Vector3.one * 0.3f);
        }
    }
}