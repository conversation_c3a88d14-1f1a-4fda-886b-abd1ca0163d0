%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: RedSPlinter
  m_Shader: {fileID: 4800000, guid: 6eb86e4931b506a408fd1477edec61ea, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - DISTORT_ON
  - WAVEUV_ON
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _ColorRampTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ColorRampTexGradient:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DistNormalMap:
        m_Texture: {fileID: 2800000, guid: 06907d08092c41f4aa8b5b3cbe5f9fa2, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DistortTex:
        m_Texture: {fileID: 2800000, guid: e4fe2219612aa4d4ba987bd61ef901c0, type: 3}
        m_Scale: {x: 3, y: 3}
        m_Offset: {x: 0, y: 0}
    - _FadeBurnTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FadeTex:
        m_Texture: {fileID: 2800000, guid: 854b25bc08516364da5a786b1cad247e, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GlowTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 4463d0563f9e01644ae5d0d3dc86a7b3, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MaskTex:
        m_Texture: {fileID: 2800000, guid: 2f6642b43dc405d498acba7e288c068c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Shape1MaskTex:
        m_Texture: {fileID: 2800000, guid: db8fa36fcfe9738418db54ee0fe17fdf, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Shape2DistortTex:
        m_Texture: {fileID: 2800000, guid: fcb027caad6963b4a80539bbce0d10e9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Shape2Tex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Shape3DistortTex:
        m_Texture: {fileID: 2800000, guid: fcb027caad6963b4a80539bbce0d10e9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Shape3Tex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShapeDistortTex:
        m_Texture: {fileID: 2800000, guid: fcb027caad6963b4a80539bbce0d10e9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TrailWidthGradient:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _VertOffsetTex:
        m_Texture: {fileID: 2800000, guid: 48da24d771916524899d36e4bfe7f762, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _Alpha: 1
    - _AlphaCutoffValue: 0.25
    - _AlphaFadeAmount: -0.1
    - _AlphaFadePow: 1
    - _AlphaFadeSmooth: 0.075
    - _AlphaStepMax: 0.075
    - _AlphaStepMin: 0
    - _CamDistFadeStepMax: 100
    - _CamDistFadeStepMin: 0
    - _CamDistProximityFade: 0
    - _ColorGradingMidPoint: 0.5
    - _ColorMask: 15
    - _ColorRampBlend: 1
    - _ColorRampLuminosity: 0
    - _CullingOption: 0
    - _DebugShape: 1
    - _DepthGlow: 1
    - _DepthGlowDist: 0.5
    - _DepthGlowGlobal: 1
    - _DepthGlowPow: 1
    - _DistortAmount: 0.05
    - _DistortTexXSpeed: 2
    - _DistortTexYSpeed: 5
    - _DistortionBlend: 1
    - _DistortionPower: 10
    - _DistortionScrollXSpeed: 0
    - _DistortionScrollYSpeed: 0
    - _DstMode: 10
    - _EditorDrawers: 60
    - _FadeAmount: -0.1
    - _FadeBurnGlow: 5
    - _FadeBurnWidth: 0.01
    - _FadePower: 1
    - _FadeScrollXSpeed: 0
    - _FadeScrollYSpeed: 0
    - _FadeTransition: 0.075
    - _Glow: 0.32
    - _GlowGlobal: 1
    - _HandDrawnAmount: 10
    - _HandDrawnSpeed: 5
    - _HsvBright: 0.492
    - _HsvSaturation: 0.763
    - _HsvShift: 254
    - _LightAmount: 0
    - _MaskPow: 1
    - _OffsetSh1: 1
    - _OffsetSh2: 1
    - _OffsetSh3: 1
    - _PixelateSize: 32
    - _PosterizeNumColors: 5
    - _RandomSh1Mult: 1
    - _RandomSh2Mult: 1
    - _RandomSh3Mult: 1
    - _RenderingMode: 0
    - _RimAddAmount: 1
    - _RimBias: 0
    - _RimErodesAlpha: 0
    - _RimIntensity: 1
    - _RimPower: 5
    - _RimScale: 1
    - _RoundWaveSpeed: 2
    - _RoundWaveStrength: 0.7
    - _ScreenUvSh2DistScale: 1
    - _ScreenUvSh3DistScale: 1
    - _ScreenUvShDistScale: 1
    - _Sh1BlendOffset: 0
    - _Sh2BlendOffset: 0
    - _Sh3BlendOffset: 0
    - _ShadowAmount: 0.4
    - _ShadowStepMax: 1
    - _ShadowStepMin: 0
    - _ShakeUvSpeed: 20
    - _ShakeUvX: 5
    - _ShakeUvY: 4
    - _Shape1MaskPow: 1
    - _Shape2AlphaWeight: 2
    - _Shape2Brightness: 0
    - _Shape2ColorWeight: 2
    - _Shape2Contrast: 1
    - _Shape2DistortAmount: 0.5
    - _Shape2DistortXSpeed: 0.1
    - _Shape2DistortYSpeed: 0.1
    - _Shape2RotationOffset: 0
    - _Shape2RotationSpeed: 0
    - _Shape2XSpeed: 0
    - _Shape2YSpeed: 0
    - _Shape3AlphaWeight: 2
    - _Shape3Brightness: 0
    - _Shape3ColorWeight: 2
    - _Shape3Contrast: 1
    - _Shape3DistortAmount: 0.5
    - _Shape3DistortXSpeed: 0.1
    - _Shape3DistortYSpeed: 0.1
    - _Shape3RotationOffset: 0
    - _Shape3RotationSpeed: 0
    - _Shape3XSpeed: 0
    - _Shape3YSpeed: 0
    - _ShapeAlphaWeight: 1
    - _ShapeBrightness: 0
    - _ShapeColorWeight: 1
    - _ShapeContrast: 1
    - _ShapeDistortAmount: 0.5
    - _ShapeDistortXSpeed: 0.1
    - _ShapeDistortYSpeed: 0.1
    - _ShapeRotationOffset: 0
    - _ShapeRotationSpeed: 0
    - _ShapeXSpeed: 0
    - _ShapeYSpeed: 0
    - _SoftFactor: 0.01
    - _SrcMode: 5
    - _TextureScrollXSpeed: 1
    - _TextureScrollYSpeed: 0
    - _TimingSeed: 0
    - _TrailWidthPower: 1
    - _TwistUvAmount: 0.01
    - _TwistUvPosX: 0.5
    - _TwistUvPosY: 0.5
    - _TwistUvRadius: 0.75
    - _VertOffsetAmount: 0.5
    - _VertOffsetPower: 1
    - _VertOffsetTexXSpeed: 0.1
    - _VertOffsetTexYSpeed: 0.1
    - _WaveAmount: 1.8
    - _WaveSpeed: 2
    - _WaveStrength: 5
    - _WaveX: 0
    - _WaveY: 0.5
    - _ZTestMode: 4
    - _ZWrite: 0
    m_Colors:
    - _BackFaceTint: {r: 0.5, g: 0.5, b: 0.5, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _ColorGradingDark: {r: 1, g: 1, b: 1, a: 1}
    - _ColorGradingLight: {r: 1, g: 1, b: 1, a: 1}
    - _ColorGradingMiddle: {r: 1, g: 1, b: 1, a: 1}
    - _DepthGlowColor: {r: 1, g: 1, b: 1, a: 1}
    - _FadeBurnColor: {r: 1, g: 1, b: 0, a: 1}
    - _FrontFaceTint: {r: 1, g: 1, b: 1, a: 1}
    - _GlowColor: {r: 1, g: 1, b: 1, a: 1}
    - _LightColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimColor: {r: 1, g: 1, b: 1, a: 1}
    - _Shape2Color: {r: 1, g: 1, b: 1, a: 1}
    - _Shape3Color: {r: 1, g: 1, b: 1, a: 1}
    - _ShapeColor: {r: 7.9066997, g: 7.9066997, b: 7.9066997, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
