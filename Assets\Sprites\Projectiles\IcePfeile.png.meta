fileFormatVersion: 2
guid: 463bb0c6a6b833a488993bcfc0f1a823
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 7770508843642864047
    second: ice_b_0
  - first:
      213: 802606489002951367
    second: ice_b_1
  - first:
      213: 4924706259225651086
    second: ice_b_2
  - first:
      213: 5348326113055910280
    second: ice_b_3
  - first:
      213: 3474210031201354021
    second: ice_b_4
  - first:
      213: -2919314489710426827
    second: ice_b_5
  - first:
      213: 5918314961588005559
    second: ice_b_6
  - first:
      213: -903321797466083245
    second: ice_b_7
  - first:
      213: -1659590615032989558
    second: ice_b_8
  - first:
      213: -9014528396930442544
    second: ice_b_9
  - first:
      213: 6573289682128605561
    second: ice_b_10
  - first:
      213: -7261035317338638858
    second: ice_b_11
  - first:
      213: -8710208716502449027
    second: ice_b_12
  - first:
      213: -4468173511692654111
    second: ice_b_13
  - first:
      213: -1193996117982567869
    second: ice_b_14
  - first:
      213: 4495630341198489827
    second: ice_b_15
  - first:
      213: -436508793080976653
    second: ice_b_16
  - first:
      213: -6152139695404033711
    second: ice_b_17
  - first:
      213: -7520674528390145505
    second: ice_b_18
  - first:
      213: 1623657568770776906
    second: ice_b_19
  - first:
      213: 2380441288629203954
    second: ice_b_20
  - first:
      213: 8617039976381538667
    second: ice_b_21
  - first:
      213: -8888580906387647108
    second: ice_b_22
  - first:
      213: -9081704296459075799
    second: ice_b_23
  - first:
      213: 1701033093571201053
    second: ice_b_24
  - first:
      213: 8598126939512561189
    second: ice_b_25
  - first:
      213: 4256502088049406114
    second: ice_b_26
  - first:
      213: 4777054445854274956
    second: ice_b_27
  - first:
      213: -5847207604608958345
    second: ice_b_28
  - first:
      213: -8448266385351090762
    second: ice_b_29
  - first:
      213: -7125617289315009614
    second: ice_b_30
  - first:
      213: -5907122859929198525
    second: ice_b_31
  - first:
      213: -7056947264533568339
    second: ice_b_32
  - first:
      213: 7228056606419156829
    second: ice_b_33
  - first:
      213: 3854378825565191323
    second: ice_b_34
  - first:
      213: -7690995873940737353
    second: ice_b_35
  - first:
      213: 4128236021249068589
    second: ice_b_36
  - first:
      213: -5967563032691665333
    second: ice_b_37
  - first:
      213: 1118050089167071758
    second: ice_b_38
  - first:
      213: 9045666848339484439
    second: ice_b_39
  - first:
      213: 9053812085501090467
    second: ice_b_40
  - first:
      213: -5486607946283322607
    second: ice_b_41
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 32
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: IcePfeile_0
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b9afd2e0e77a06c4b9c8916fa806b7a4
      internalID: -1777642828
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_1
      rect:
        serializedVersion: 2
        x: 64
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 660378d3420cabc47be20cf819c95313
      internalID: -2033377888
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_2
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 48e7dcf63775e974b943302c78b6e347
      internalID: -468089209
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_3
      rect:
        serializedVersion: 2
        x: 192
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2f5f98ee4edd4d340868ba26fc502011
      internalID: 1745967249
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_4
      rect:
        serializedVersion: 2
        x: 256
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: eaf543a08dcf49d428947f6bb2524d84
      internalID: -845095537
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_5
      rect:
        serializedVersion: 2
        x: 320
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 16a16a903dcb6cf46bdc75480fe1b8d1
      internalID: 1283198240
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_6
      rect:
        serializedVersion: 2
        x: 384
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a1c7c40eb00dd4b42b807c29bc587ae0
      internalID: -1663075328
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_7
      rect:
        serializedVersion: 2
        x: 448
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9197658cc31e23d4e9ef3442c7ead16e
      internalID: -74769021
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_8
      rect:
        serializedVersion: 2
        x: 512
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e51e0928b8d220c4da6c859c6c1ed9c2
      internalID: 1953044018
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_9
      rect:
        serializedVersion: 2
        x: 576
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: aa9bf6ff98fc875469b41d1d09af713a
      internalID: 105236725
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_10
      rect:
        serializedVersion: 2
        x: 640
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 585f1ee464fd31b4d94c2b82b8fe4ed6
      internalID: 567225605
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_11
      rect:
        serializedVersion: 2
        x: 704
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 389bc666b9a05544ca02e838cbfd9af8
      internalID: 863255836
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_12
      rect:
        serializedVersion: 2
        x: 768
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5b1e720b16fbe284b9d9f371cda5f3e9
      internalID: 1166671059
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_13
      rect:
        serializedVersion: 2
        x: 832
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cddbad39f1effe24e919590fad427d58
      internalID: 1648275119
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_14
      rect:
        serializedVersion: 2
        x: 896
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b00eb5a86ec8a764aa96a9a9fa6afff1
      internalID: 8264924
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_15
      rect:
        serializedVersion: 2
        x: 960
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 574398e28421f1948bc3888fffedc6a3
      internalID: -1455264954
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_16
      rect:
        serializedVersion: 2
        x: 1024
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f8102a9b765c831459b64934c7087ad2
      internalID: 1611083057
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_17
      rect:
        serializedVersion: 2
        x: 1088
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9a60337f147f997479fafe2e34223cea
      internalID: -1139484812
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_18
      rect:
        serializedVersion: 2
        x: 1152
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: aaec55dfc22428046960dc78b45956e6
      internalID: -2126710677
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_19
      rect:
        serializedVersion: 2
        x: 1216
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1802cb0bee291ec46ab91d8d5be5ec6c
      internalID: 117714968
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_20
      rect:
        serializedVersion: 2
        x: 1280
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 561ccd43e0436254a80ff1d10fadbcb5
      internalID: -433284187
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_21
      rect:
        serializedVersion: 2
        x: 1344
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 17c6146ece831ed40adb76c1bfdbf1f8
      internalID: 1939132130
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: IcePfeile_22
      rect:
        serializedVersion: 2
        x: 1408
        y: 0
        width: 64
        height: 96
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4b476992377ad1245ad9835024298c2c
      internalID: 519653812
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 26a0105b99753ed46a7369f538d4cedb
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries:
      - key: SpriteEditor.SliceSettings
        value: '{"sliceOnImport":false,"gridCellCount":{"x":23.0,"y":1.0},"gridSpriteSize":{"x":64.0,"y":96.0},"gridSpriteOffset":{"x":0.0,"y":0.0},"gridSpritePadding":{"x":0.0,"y":0.0},"pivot":{"x":0.5,"y":0.0},"autoSlicingMethod":0,"spriteAlignment":7,"slicingType":2,"keepEmptyRects":false,"isAlternate":false}'
    nameFileIdTable:
      IcePfeile_0: -1777642828
      IcePfeile_1: -2033377888
      IcePfeile_10: 567225605
      IcePfeile_11: 863255836
      IcePfeile_12: 1166671059
      IcePfeile_13: 1648275119
      IcePfeile_14: 8264924
      IcePfeile_15: -1455264954
      IcePfeile_16: 1611083057
      IcePfeile_17: -1139484812
      IcePfeile_18: -2126710677
      IcePfeile_19: 117714968
      IcePfeile_2: -468089209
      IcePfeile_20: -433284187
      IcePfeile_21: 1939132130
      IcePfeile_22: 519653812
      IcePfeile_3: 1745967249
      IcePfeile_4: -845095537
      IcePfeile_5: 1283198240
      IcePfeile_6: -1663075328
      IcePfeile_7: -74769021
      IcePfeile_8: 1953044018
      IcePfeile_9: 105236725
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
