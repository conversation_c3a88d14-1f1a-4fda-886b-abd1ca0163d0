using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using Sirenix.OdinInspector;
using TMPro;

/// <summary>
/// Diamond-shaped material slot for crafting interface.
/// Handles click-to-select material behavior and right-click removal.
/// </summary>
public class CraftingMaterialSlot : Mono<PERSON><PERSON><PERSON>our, IPointerClickHandler, IPointerEnterHandler, IPointerExitHandler
{
    [Title("References")]
    [Required]
    [InfoBox("LayeredIconDisplay for showing material icons")]
    public LayeredIconDisplay layeredIconDisplay;
    
    [Required]
    [InfoBox("Border image for visual feedback")]
    public Image borderImage;
    
    [InfoBox("Optional transform reference (no automatic rotation applied)")]
    public Transform diamondTransform;
    
    
    [Title("Visual Settings")]
    [InfoBox("Color for empty slot border")]
    public Color emptyBorderColor = Color.gray;
    
    [InfoBox("Color for filled slot border")]
    public Color filledBorderColor = Color.white;
    
    [InfoBox("Color for hover state")]
    public Color hoverBorderColor = Color.yellow;
    
    [Title("Canvas Group")]
    [InfoBox("Optional canvas group for alpha fading")]
    public CanvasGroup canvasGroup;
    
    [Title("State")]
    [ReadOnly]
    [ShowInInspector]
    private CraftingMaterialInstance currentMaterial;
    
    [ReadOnly]
    [ShowInInspector]
    private int slotIndex;
    
    private CraftingPanel craftingPanel;
    private bool isInitialized = false;
    
    // Events
    public System.Action<int, CraftingMaterialInstance> OnMaterialChanged;
    
    public bool IsEmpty => currentMaterial == null;
    public CraftingMaterialInstance CurrentMaterial => currentMaterial;
    public int SlotIndex => slotIndex;
    
    #region Unity Lifecycle
    
    private void Awake()
    {
        // Cache CanvasGroup reference if not assigned
        if (canvasGroup == null)
            canvasGroup = GetComponent<CanvasGroup>();
        
        UpdateVisuals();
    }
    
    #endregion
    
    #region Initialization
    
    public void Initialize(CraftingPanel panel, int index)
    {
        craftingPanel = panel;
        slotIndex = index;
        isInitialized = true;
        
        UpdateVisuals();
    }
    
    
    #endregion
    
    #region Material Management
    
    public void SetMaterial(CraftingMaterialInstance material)
    {
        var previousMaterial = currentMaterial;
        currentMaterial = material;
        
        UpdateVisuals();
        
        // Notify of change
        if (previousMaterial != material)
        {
            OnMaterialChanged?.Invoke(slotIndex, currentMaterial);
        }
    }
    
    public CraftingMaterialInstance RemoveMaterial()
    {
        var material = currentMaterial;
        SetMaterial(null);
        return material;
    }
    
    public bool CanAcceptMaterial(CraftingMaterialData materialData)
    {
        // Can accept if slot is empty
        return IsEmpty;
    }
    
    #endregion
    
    #region Visual Updates
    
    private void UpdateVisuals()
    {
        if (layeredIconDisplay != null)
        {
            if (currentMaterial != null)
            {
                // Show material icon
                if (currentMaterial.HasLayeredIcons())
                {
                    layeredIconDisplay.SetManualLayeredIcon(
                        currentMaterial.GetBackgroundIcon(),
                        currentMaterial.GetForegroundIcon()
                    );
                }
                else
                {
                    layeredIconDisplay.SetSingleIcon(currentMaterial.GetIcon());
                }
            }
            else
            {
                // Clear icon for empty slot
                layeredIconDisplay.ClearIcon();
            }
        }
        
        UpdateBorderColor();
    }
    
    
    private void UpdateBorderColor()
    {
        if (borderImage != null)
        {
            borderImage.color = IsEmpty ? emptyBorderColor : filledBorderColor;
        }
    }
    
    private void SetHoverState(bool isHovering)
    {
        if (borderImage != null && isInitialized)
        {
            if (isHovering)
            {
                borderImage.color = hoverBorderColor;
            }
            else
            {
                UpdateBorderColor();
            }
        }
    }
    
    #endregion
    
    #region Event Handlers
    
    public void OnPointerClick(PointerEventData eventData)
    {
        if (!isInitialized || craftingPanel == null)
            return;
        
        if (eventData.button == PointerEventData.InputButton.Left)
        {
            HandleLeftClick();
        }
        else if (eventData.button == PointerEventData.InputButton.Right)
        {
            HandleRightClick();
        }
    }
    
    public void OnPointerEnter(PointerEventData eventData)
    {
        SetHoverState(true);
        
        // Show tooltip if material is present
        if (currentMaterial != null && craftingPanel != null)
        {
            Vector3 tooltipPosition = transform.position;
            craftingPanel.ShowTooltip(currentMaterial, tooltipPosition);
        }
    }
    
    public void OnPointerExit(PointerEventData eventData)
    {
        SetHoverState(false);
        
        // Hide tooltip
        if (craftingPanel != null)
        {
            craftingPanel.HideTooltip();
        }
    }
    
    private void HandleLeftClick()
    {
        if (IsEmpty)
        {
            // Empty slot clicked - no action needed
            // Materials are now added via MaterialIconButton components
            Debug.Log($"[CraftingMaterialSlot] Clicked empty slot {slotIndex} - use material icons to add materials");
        }
        else
        {
            // Could show material details or allow replacement
            // For now, just show tooltip information
            Debug.Log($"[CraftingMaterialSlot] Clicked on material: {currentMaterial.GetDisplayName()}");
        }
    }
    
    private void HandleRightClick()
    {
        if (!IsEmpty)
        {
            // Remove material and return to inventory
            RemoveMaterialFromSlot();
        }
    }
    
    #endregion
    
    
    #region Material Management Helper
    
    private void RemoveMaterialFromSlot()
    {
        if (currentMaterial == null)
            return;
        
        // Return material to inventory
        CraftingMaterialManager.AddMaterial(currentMaterial.materialDataTemplate, currentMaterial.stackCount);
        
        // Clear slot
        SetMaterial(null);
        
        Debug.Log($"[CraftingMaterialSlot] Removed material from slot {slotIndex}");
    }
    
    #endregion
    
    #region Debug
    
    [Title("Debug")]
    [Button("Test Add Random Material")]
    [PropertyOrder(20)]
    private void DebugAddRandomMaterial()
    {
        var availableMaterials = CraftingMaterialManager.GetAvailableMaterialTypes();
        
        if (availableMaterials.Count > 0)
        {
            var randomMaterial = availableMaterials[Random.Range(0, availableMaterials.Count)];
            var testInstance = new CraftingMaterialInstance(randomMaterial, Random.Range(1, 5));
            SetMaterial(testInstance);
            Debug.Log($"[CraftingMaterialSlot] Debug: Added {testInstance.GetDisplayName()} x{testInstance.stackCount}");
        }
        else
        {
            Debug.Log("[CraftingMaterialSlot] Debug: No materials available");
        }
    }
    
    [Button("Clear Material")]
    [PropertyOrder(21)]
    private void DebugClearMaterial()
    {
        SetMaterial(null);
        Debug.Log($"[CraftingMaterialSlot] Debug: Cleared slot {slotIndex}");
    }
    
    [Button("Log Current State")]
    [PropertyOrder(22)]
    private void DebugLogState()
    {
        string materialInfo = currentMaterial != null 
            ? $"{currentMaterial.GetDisplayName()} x{currentMaterial.stackCount}"
            : "Empty";
            
        Debug.Log($"[CraftingMaterialSlot] Slot {slotIndex}: {materialInfo}");
    }
    
    #endregion
}