using UnityEngine;
using UnityEngine.UI;
using Sirenix.OdinInspector;
using TMPro;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Crafting panel UI manager following InventoryManager pattern.
/// Handles the crafting interface with 4 diamond material slots and central craft button.
/// </summary>
public class CraftingPanel : MonoBehaviour
{
    [Title("References")]
    [Required]
    [InfoBox("Main crafting panel GameObject that will be toggled")]
    public GameObject craftingPanel;
    
    [Title("Material Slots")]
    [Required]
    [InfoBox("Four diamond-shaped material slots around the craft button")]
    [ListDrawerSettings(ShowIndexLabels = true, DraggableItems = false, NumberOfItemsPerPage = 4)]
    public List<CraftingMaterialSlot> materialSlots = new List<CraftingMaterialSlot>(4);
    
    [Title("Craft Button")]
    [Required]
    [InfoBox("Central craft button to create gems")]
    public Button craftButton;
    
    [Required]
    [InfoBox("Text component on the craft button")]
    public TextMeshProUGUI craftButtonText;
    
    [Title("Result Area")]
    [Required]
    [InfoBox("Area where the crafted gem will appear")]
    public Transform resultArea;
    
    [InfoBox("Icon display for showing the result gem")]
    public LayeredIconDisplay resultIconDisplay;
    
    [Title("Animation")]
    [InfoBox("Optional PrimeTweenRectTransformAnimator for smooth panel animations")]
    public PrimeTweenRectTransformAnimator panelAnimation;
    
    [Title("Tooltip")]
    [Required]
    public GameObject tooltipObject;
    
    [Required]
    public TextMeshProUGUI tooltipText;
    
    [Title("Splinter Progress Display")]
    [InfoBox("Shows splinter collection progress. Format: '23/50' - manually positioned in inspector")]
    
    [SerializeField]
    [InfoBox("Red splinter progress text")]
    public TextMeshProUGUI redSplinterText;
    
    [SerializeField]
    [InfoBox("Blue splinter progress text")]
    public TextMeshProUGUI blueSplinterText;
    
    [SerializeField]
    [InfoBox("Green splinter progress text")]
    public TextMeshProUGUI greenSplinterText;
    
    [SerializeField]
    [InfoBox("Yellow splinter progress text")]
    public TextMeshProUGUI yellowSplinterText;
    
    [SerializeField]
    [InfoBox("Chaos splinter progress text")]
    public TextMeshProUGUI chaosSplinterText;
    
    [SerializeField]
    [InfoBox("Purple splinter progress text")]
    public TextMeshProUGUI purpleSplinterText;
    
    [Title("Material Inventory Display")]
    [InfoBox("Shows converted materials. Format: '5' - manually positioned in inspector")]
    
    [SerializeField]
    [InfoBox("Red crystal material count text")]
    public TextMeshProUGUI redMaterialText;
    
    [SerializeField]
    [InfoBox("Blue crystal material count text")]
    public TextMeshProUGUI blueMaterialText;
    
    [SerializeField]
    [InfoBox("Green crystal material count text")]
    public TextMeshProUGUI greenMaterialText;
    
    [SerializeField]
    [InfoBox("Yellow crystal material count text")]
    public TextMeshProUGUI yellowMaterialText;
    
    [SerializeField]
    [InfoBox("Chaos crystal material count text")]
    public TextMeshProUGUI chaosMaterialText;
    
    [SerializeField]
    [InfoBox("Purple crystal material count text")]
    public TextMeshProUGUI purpleMaterialText;
    
    [Title("Debug Settings")]
    [SerializeField]
    [Tooltip("Enable debug logging for CraftingPanel")]
    private bool enableDebugLogging = false;
    
    [Title("Input")]
    [SerializeField, ShowInInspector, InfoBox("Taste zum Öffnen/Schließen des Crafting-Panels")]
    private KeyCode toggleKey = KeyCode.C;
    
    [Title("Material Icons")]
    [InfoBox("Material icon buttons for adding materials to slots")]
    [ListDrawerSettings(ShowIndexLabels = true, DraggableItems = false)]
    public List<MaterialIconButton> materialIconButtons = new List<MaterialIconButton>();
    
    
    // State
    private bool isOpen = false;
    private CraftingMaterialInstance[] sockettedMaterials = new CraftingMaterialInstance[4];
    
    // Events
    public System.Action<GemInstance> OnGemCrafted;
    
    #region Unity Lifecycle
    
    private void Awake()
    {
        InitializeCraftingPanel();
        HideTooltip();
        
        if (craftingPanel != null)
            craftingPanel.SetActive(false);
    }
    
    private void Start()
    {
        
        ValidateSetup();
        SetupCraftButton();
        SetupMaterialIconButtons();
    }
    
    private void OnEnable()
    {
        // Subscribe to both splinter and material inventory changes for static display updates
        if (SplinterCollectionManager.Instance != null)
        {
            SplinterCollectionManager.Instance.OnSplinterInventoryChanged.AddListener(OnInventoryChanged);
            SplinterCollectionManager.Instance.OnMaterialGenerated.AddListener(OnMaterialGenerated);
        }
        
        CraftingMaterialManager.OnMaterialInventoryChanged += OnInventoryChanged;
        
        if (enableDebugLogging)
        {
            Debug.Log("[CraftingPanel] Subscribed to splinter inventory events");
        }
    }
    
    private void OnDisable()
    {
        // Unsubscribe from both splinter and material inventory events
        if (SplinterCollectionManager.Instance != null)
        {
            SplinterCollectionManager.Instance.OnSplinterInventoryChanged.RemoveListener(OnInventoryChanged);
            SplinterCollectionManager.Instance.OnMaterialGenerated.RemoveListener(OnMaterialGenerated);
        }
        
        CraftingMaterialManager.OnMaterialInventoryChanged -= OnInventoryChanged;
        
        if (enableDebugLogging)
        {
            Debug.Log("[CraftingPanel] Unsubscribed from splinter inventory events");
        }
    }
    
    private void OnDestroy()
    {
        // Clean up events
        if (craftButton != null)
            craftButton.onClick.RemoveListener(OnCraftButtonClicked);
            
        // Clean up slot events
        foreach (var slot in materialSlots)
        {
            if (slot != null)
            {
                slot.OnMaterialChanged -= OnSlotMaterialChanged;
            }
        }
    }
    
    private void Update()
    {
        if (Input.GetKeyDown(toggleKey))
        {
            ToggleCrafting();
        }
    }
    
    #endregion
    
    #region Initialization
    
    private void InitializeCraftingPanel()
    {
        // Initialize socketed materials array
        sockettedMaterials = new CraftingMaterialInstance[4];
        
        // Setup material slots
        for (int i = 0; i < materialSlots.Count && i < 4; i++)
        {
            if (materialSlots[i] != null)
            {
                materialSlots[i].Initialize(this, i);
                materialSlots[i].OnMaterialChanged += OnSlotMaterialChanged;
            }
        }
        
        if (enableDebugLogging)
        {
            Debug.Log($"[CraftingPanel] Initialized {materialSlots.Count} material slots");
        }
    }
    
    private void ValidateSetup()
    {
        bool hasErrors = false;
        
        if (craftingPanel == null)
        {
            Debug.LogError("[CraftingPanel] craftingPanel is not assigned!");
            hasErrors = true;
        }
        
        if (materialSlots.Count != 4)
        {
            Debug.LogError($"[CraftingPanel] Expected 4 material slots, found {materialSlots.Count}!");
            hasErrors = true;
        }
        
        for (int i = 0; i < materialSlots.Count; i++)
        {
            if (materialSlots[i] == null)
            {
                Debug.LogError($"[CraftingPanel] Material slot {i} is null!");
                hasErrors = true;
            }
        }
        
        if (craftButton == null)
        {
            Debug.LogError("[CraftingPanel] craftButton is not assigned!");
            hasErrors = true;
        }
        
        
        if (!hasErrors && enableDebugLogging)
        {
            Debug.Log("[CraftingPanel] Setup validation passed");
        }
    }
    
    private void SetupCraftButton()
    {
        if (craftButton != null)
        {
            craftButton.onClick.AddListener(OnCraftButtonClicked);
            UpdateCraftButtonState();
        }
        
        if (craftButtonText != null)
        {
            craftButtonText.text = "Craft";
        }
    }
    
    private void SetupMaterialIconButtons()
    {
        // Auto-find MaterialIconButton components if not assigned
        if (materialIconButtons.Count == 0)
        {
            var foundButtons = FindObjectsByType<MaterialIconButton>(FindObjectsSortMode.None);
            materialIconButtons.AddRange(foundButtons);
            
            if (enableDebugLogging && materialIconButtons.Count > 0)
            {
                Debug.Log($"[CraftingPanel] Auto-found {materialIconButtons.Count} MaterialIconButton components");
            }
        }
        
        // Initialize each button with this panel reference
        foreach (var button in materialIconButtons)
        {
            if (button != null)
            {
                // The MaterialIconButton will auto-find this panel, but we can provide direct reference
                if (enableDebugLogging)
                {
                    Debug.Log($"[CraftingPanel] Setting up MaterialIconButton for: {button.MaterialData?.materialName ?? "unknown"}");
                }
            }
        }
        
        if (materialIconButtons.Count == 0)
        {
            Debug.LogWarning("[CraftingPanel] No MaterialIconButton components found. Assign them manually or ensure they exist in the scene.");
        }
    }
    
    #endregion
    
    #region Panel Management
    
    public void ToggleCrafting()
    {
        if (enableDebugLogging)
        {
            Debug.Log($"[CraftingPanel] ToggleCrafting called. Current isOpen={isOpen}");
        }
        
        // Check if animation is currently playing
        if (IsAnimatorPlaying(panelAnimation))
        {
            if (enableDebugLogging)
            {
                Debug.Log("[CraftingPanel] Animation in progress, ignoring toggle request");
            }
            return;
        }
        
        isOpen = !isOpen;
        
        if (isOpen)
        {
            StartOpenAnimation();
        }
        else
        {
            StartCloseAnimation();
        }
    }
    
    public void OpenCrafting()
    {
        if (!isOpen)
        {
            ToggleCrafting();
        }
    }
    
    public void CloseCrafting()
    {
        if (isOpen)
        {
            ToggleCrafting();
        }
    }
    
    private void StartOpenAnimation()
    {
        if (enableDebugLogging)
        {
            Debug.Log("[CraftingPanel] Starting open animation");
        }
        
        // Clear any previous result display
        ClearResultDisplay();
        
        if (craftingPanel != null)
        {
            craftingPanel.SetActive(true);
            
            if (panelAnimation != null)
            {
                panelAnimation.PlayOpenAnimation();
            }
        }
        
        RefreshDisplay();
        RefreshMaterialIconButtons();
        Canvas.ForceUpdateCanvases();
    }
    
    private void StartCloseAnimation()
    {
        if (enableDebugLogging)
        {
            Debug.Log("[CraftingPanel] Starting close animation");
        }
        
        HideTooltip();
        
        if (craftingPanel != null && panelAnimation != null)
        {
            System.Action closeCallback = null;
            closeCallback = () =>
            {
                panelAnimation.OnCloseComplete -= closeCallback;
                OnCloseAnimationComplete();
            };
            
            panelAnimation.OnCloseComplete += closeCallback;
            panelAnimation.PlayCloseAnimation();
        }
        else if (craftingPanel != null)
        {
            craftingPanel.SetActive(false);
            OnCloseAnimationComplete();
        }
    }
    
    private void OnCloseAnimationComplete()
    {
        if (enableDebugLogging)
        {
            Debug.Log("[CraftingPanel] Close animation completed");
        }
        
    }
    
    private bool IsAnimatorPlaying(PrimeTweenRectTransformAnimator animator)
    {
        if (animator == null) return false;
        
        var field = typeof(PrimeTweenRectTransformAnimator).GetField("isAnimating", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (field != null)
        {
            return (bool)field.GetValue(animator);
        }
        
        return false;
    }
    
    #endregion
    
    #region Material Management
    
    public void SetMaterialInSlot(int slotIndex, CraftingMaterialInstance material)
    {
        if (slotIndex < 0 || slotIndex >= sockettedMaterials.Length)
        {
            Debug.LogWarning($"[CraftingPanel] Invalid slot index: {slotIndex}");
            return;
        }
        
        // Remove previous material if any
        if (sockettedMaterials[slotIndex] != null)
        {
            RemoveMaterialFromSlot(slotIndex);
        }
        
        // Set new material
        sockettedMaterials[slotIndex] = material;

        // Update slot display
        if (slotIndex < materialSlots.Count && materialSlots[slotIndex] != null)
        {
            materialSlots[slotIndex].SetMaterial(material);
        }

        // Clear any previous result display when new materials are added
        ClearResultDisplay();

        // Reset craft button text to default state when materials change
        ResetCraftButtonText();

        UpdateCraftButtonState();
        
        if (enableDebugLogging)
        {
            string materialName = material?.GetDisplayName() ?? "null";
            Debug.Log($"[CraftingPanel] Set material in slot {slotIndex}: {materialName}");
        }
    }
    
    public void RemoveMaterialFromSlot(int slotIndex)
    {
        if (slotIndex < 0 || slotIndex >= sockettedMaterials.Length)
        {
            Debug.LogWarning($"[CraftingPanel] Invalid slot index: {slotIndex}");
            return;
        }
        
        var removedMaterial = sockettedMaterials[slotIndex];
        if (removedMaterial != null)
        {
            // Return material to inventory
            CraftingMaterialManager.AddMaterial(removedMaterial.materialDataTemplate, removedMaterial.stackCount);
            
            // Clear slot
            sockettedMaterials[slotIndex] = null;
            
            // Update slot display
            if (slotIndex < materialSlots.Count && materialSlots[slotIndex] != null)
            {
                materialSlots[slotIndex].SetMaterial(null);
            }

            // Clear any previous result display when materials are removed
            ClearResultDisplay();

            // Reset craft button text to default state when materials change
            ResetCraftButtonText();

            UpdateCraftButtonState();
            
            if (enableDebugLogging)
            {
                Debug.Log($"[CraftingPanel] Removed material from slot {slotIndex}: {removedMaterial.GetDisplayName()}");
            }
        }
    }
    
    public CraftingMaterialInstance GetMaterialInSlot(int slotIndex)
    {
        if (slotIndex < 0 || slotIndex >= sockettedMaterials.Length)
            return null;
            
        return sockettedMaterials[slotIndex];
    }
    
    public List<CraftingMaterialInstance> GetAllSockettedMaterials()
    {
        var materials = new List<CraftingMaterialInstance>();
        
        foreach (var material in sockettedMaterials)
        {
            if (material != null)
            {
                materials.Add(material);
            }
        }
        
        return materials;
    }
    
    public bool HasMaterialsForCrafting()
    {
        foreach (var material in sockettedMaterials)
        {
            if (material != null)
            {
                return true;
            }
        }
        return false;
    }
    
    #endregion
    
    #region Crafting Logic
    
    private void UpdateCraftButtonState()
    {
        if (craftButton != null)
        {
            bool canCraft = HasMaterialsForCrafting();
            craftButton.interactable = canCraft;
            
            if (craftButtonText != null)
            {
                // Reset to default states unless there's an error or success condition
                string currentText = craftButtonText.text;
                bool isErrorState = currentText.Contains("Error") ||
                                   currentText.Contains("Failed") ||
                                   currentText.Contains("No Gem") ||
                                   currentText.Contains("System");
                bool isSuccessState = currentText.Contains("Success") ||
                                     currentText.Contains("Added to Inventory");

                // Only update if not in error/success state, or if we need to show material requirement
                if ((!isErrorState && !isSuccessState) || !canCraft)
                {
                    craftButtonText.text = canCraft ? "Craft" : "Need Materials";
                }
            }
        }
        
        if (enableDebugLogging)
        {
            bool canCraft = HasMaterialsForCrafting();
            int materialCount = GetAllSockettedMaterials().Count;
            Debug.Log($"[CraftingPanel] UpdateCraftButtonState - CanCraft: {canCraft}, Materials: {materialCount}");
        }
    }
    
    private void ClearResultDisplay()
    {
        if (resultIconDisplay != null)
        {
            resultIconDisplay.ClearIcon();
            
            if (enableDebugLogging)
            {
                Debug.Log("[CraftingPanel] Cleared result display");
            }
        }
    }
    
    private void ResetCraftButtonText()
    {
        if (craftButtonText != null)
        {
            bool canCraft = HasMaterialsForCrafting();
            craftButtonText.text = canCraft ? "Craft" : "Need Materials";
            
            if (enableDebugLogging)
            {
                Debug.Log($"[CraftingPanel] Reset craft button text to: {craftButtonText.text}");
            }
        }
    }
    
    private void OnCraftButtonClicked()
    {
        // Check if this is a "New Craft" button click (after successful crafting)
        if (craftButtonText != null && craftButtonText.text == "New Craft")
        {
            StartNewCraft();
            return;
        }
        
        Debug.Log("=== [CraftingPanel] CRAFTING PROCESS STARTED ===");
        
        if (!HasMaterialsForCrafting())
        {
            Debug.LogWarning("[CraftingPanel] Cannot craft - no materials socketed");
            return;
        }
        
        // Log current setup state for debugging
        Debug.Log($"[CraftingPanel] Craft button clicked - starting crafting process");
        Debug.Log($"[CraftingPanel] Scene setup check:");
        Debug.Log($"  - resultIconDisplay assigned: {resultIconDisplay != null}");
        Debug.Log($"  - GemConfigurationManager exists: {GemConfigurationManager.Instance != null}");
        Debug.Log($"  - GemManager in scene: {FindFirstObjectByType<GemManager>() != null}");
        
        var materials = GetAllSockettedMaterials();
        Debug.Log($"[CraftingPanel] Found {materials.Count} socketed materials:");
        foreach (var material in materials)
        {
            Debug.Log($"  - {material.GetDisplayName()} x{material.stackCount}");
        }
        
        PerformCrafting();
        
        Debug.Log("=== [CraftingPanel] CRAFTING PROCESS COMPLETED ===");
    }
    
    private void PerformCrafting()
    {
        // Get socketed materials
        var materials = GetAllSockettedMaterials();
        
        if (materials.Count == 0)
        {
            Debug.LogWarning("[CraftingPanel] No materials found for crafting");
            return;
        }
        
        if (enableDebugLogging)
        {
            Debug.Log($"[CraftingPanel] Crafting with {materials.Count} materials:");
            foreach (var material in materials)
            {
                Debug.Log($"  - {material.GetDisplayName()} x{material.stackCount}");
            }
        }
        
        // Create gem based on material influences
        var craftedGem = CreateGemFromMaterials(materials);
        
        if (craftedGem != null)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[CraftingPanel] Crafted gem successfully created: {craftedGem.DisplayName}");
            }
            
            // Show result in result area
            Debug.Log($"[CraftingPanel] Attempting to display result: {craftedGem.DisplayName}");
            Debug.Log($"[CraftingPanel] resultIconDisplay is null: {resultIconDisplay == null}");
            
            if (resultIconDisplay != null)
            {
                try
                {
                    Debug.Log($"[CraftingPanel] Calling resultIconDisplay.SetGem() with: {craftedGem.DisplayName}");
                    Debug.Log($"[CraftingPanel] Gem icon: {craftedGem.Icon?.name ?? "null"}");
                    Debug.Log($"[CraftingPanel] Gem data template: {craftedGem.gemDataTemplate?.gemName ?? "null"}");
                    
                    resultIconDisplay.SetGem(craftedGem);
                    
                    Debug.Log($"[CraftingPanel] SetGem() call completed - checking if display updated");
                    
                    // Check if the LayeredIconDisplay components are properly activated
                    var runeImage = resultIconDisplay.GetType().GetField("runeImage", 
                        System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance)?.GetValue(resultIconDisplay) as UnityEngine.UI.Image;
                    var symbolImage = resultIconDisplay.GetType().GetField("symbolImage", 
                        System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance)?.GetValue(resultIconDisplay) as UnityEngine.UI.Image;
                    
                    Debug.Log($"[CraftingPanel] LayeredIconDisplay state after SetGem:");
                    Debug.Log($"  - runeImage exists: {runeImage != null}");
                    Debug.Log($"  - runeImage enabled: {runeImage?.enabled ?? false}");
                    Debug.Log($"  - runeImage sprite: {(runeImage?.sprite != null ? runeImage.sprite.name : "null")}");
                    Debug.Log($"  - symbolImage exists: {symbolImage != null}");
                    Debug.Log($"  - symbolImage enabled: {symbolImage?.enabled ?? false}");
                    Debug.Log($"  - symbolImage sprite: {(symbolImage?.sprite != null ? symbolImage.sprite.name : "null")}");
                    Debug.Log($"  - resultIconDisplay GameObject active: {resultIconDisplay.gameObject.activeInHierarchy}");
                    Debug.Log($"  - resultIconDisplay GameObject name: {resultIconDisplay.gameObject.name}");
                    Debug.Log($"  - runeImage GameObject active: {(runeImage != null ? runeImage.gameObject.activeInHierarchy : false)}");
                    Debug.Log($"  - runeImage GameObject name: {(runeImage != null ? runeImage.gameObject.name : "null")}");
                    
                    if (enableDebugLogging)
                    {
                        Debug.Log($"[CraftingPanel] Successfully displayed crafted gem in result area: {craftedGem.DisplayName}");
                    }
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"[CraftingPanel] ERROR: Failed to display result in resultIconDisplay: {ex.Message}");
                    Debug.LogError($"[CraftingPanel] Stack trace: {ex.StackTrace}");
                    Debug.LogError($"[CraftingPanel] resultIconDisplay component might be misconfigured");
                }
            }
            else
            {
                Debug.LogError("[CraftingPanel] SETUP ERROR: resultIconDisplay is null! Please assign LayeredIconDisplay component in CraftingPanel inspector");
                Debug.LogError("[CraftingPanel] Result will not be visible to player, but gem will still be added to inventory");
            }
            
            // Add gem to inventory
            var gemManager = FindFirstObjectByType<GemManager>();
            if (gemManager != null)
            {
                try
                {
                    gemManager.AddGemToInventory(craftedGem.gemDataTemplate, false, craftedGem.rarity);
                    
                    if (enableDebugLogging)
                    {
                        Debug.Log($"[CraftingPanel] Successfully added to inventory: {craftedGem.DisplayName}");
                    }
                    
                    // Update craft button to show new craft option
                    if (craftButtonText != null)
                    {
                        craftButtonText.text = "New Craft";
                    }
                    
                    // Notify listeners
                    OnGemCrafted?.Invoke(craftedGem);
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"[CraftingPanel] ERROR: Failed to add gem to inventory: {ex.Message}");
                    
                    // Update craft button to show error
                    if (craftButtonText != null)
                    {
                        craftButtonText.text = "Inventory Error";
                    }
                }
            }
            else
            {
                Debug.LogError("[CraftingPanel] SETUP ERROR: GemManager not found in scene!");
                Debug.LogError("[CraftingPanel] Cannot add crafted gem to inventory - gem will be lost");
                
                // Update craft button to show error
                if (craftButtonText != null)
                {
                    craftButtonText.text = "No Inventory Mgr";
                }
            }
        }
        else
        {
            Debug.LogError("[CraftingPanel] CRAFTING FAILED: CreateGemFromMaterials returned null");
            Debug.LogError("[CraftingPanel] Check the error messages above for the specific cause");
            
            // Update craft button to show failure (if not already updated by CreateGemFromMaterials)
            if (craftButtonText != null && craftButtonText.text == "Craft")
            {
                craftButtonText.text = "Craft Failed";
            }
        }
        
        // Clear all slots (materials are consumed)
        // Temporarily disable event handling to prevent clearing the result display
        bool previousDebugState = enableDebugLogging;
        for (int i = 0; i < sockettedMaterials.Length; i++)
        {
            sockettedMaterials[i] = null;
            if (i < materialSlots.Count && materialSlots[i] != null)
            {
                // Temporarily unsubscribe from OnMaterialChanged to prevent ClearResultDisplay calls
                materialSlots[i].OnMaterialChanged -= OnSlotMaterialChanged;
                materialSlots[i].SetMaterial(null);
                materialSlots[i].OnMaterialChanged += OnSlotMaterialChanged;
            }
        }

        // Don't call UpdateCraftButtonState() after successful crafting
        // as it would reset the success message and potentially interfere with result display
        // The button state will be updated when materials are added again or panel is reopened
    }
    
    private void StartNewCraft()
    {
        if (enableDebugLogging)
        {
            Debug.Log("[CraftingPanel] Starting new craft - clearing result and resetting UI");
        }
        
        // Clear the result display
        ClearResultDisplay();
        
        // Reset craft button to normal state
        ResetCraftButtonText();
        
        // Update button state based on current materials
        UpdateCraftButtonState();
        
        if (enableDebugLogging)
        {
            Debug.Log("[CraftingPanel] New craft ready - panel reset complete");
        }
    }
    
    private GemInstance CreateGemFromMaterials(List<CraftingMaterialInstance> materials)
    {
        if (enableDebugLogging)
        {
            Debug.Log($"[CraftingPanel] CreateGemFromMaterials called with {materials.Count} materials");
        }
        
        // Get available gems from configuration
        var gemConfigManager = GemConfigurationManager.Instance;
        if (gemConfigManager == null)
        {
            Debug.LogError("[CraftingPanel] CRITICAL ERROR: GemConfigurationManager.Instance is null! Please ensure:");
            Debug.LogError("1. GemConfigurationManager component exists in the scene");
            Debug.LogError("2. GemConfigurationManager is properly initialized");
            Debug.LogError("3. Check script execution order");
            
            // Show user-friendly error in craft button
            if (craftButtonText != null)
            {
                craftButtonText.text = "System Error";
            }
            return null;
        }
        
        var allGems = gemConfigManager.GetAllGems();
        if (allGems.Count == 0)
        {
            Debug.LogError("[CraftingPanel] CRITICAL ERROR: No gems available in GemConfiguration! Please ensure:");
            Debug.LogError("1. GemConfiguration ScriptableObject is assigned to GemConfigurationManager");
            Debug.LogError("2. GemConfiguration asset contains gem data");
            Debug.LogError("3. Gem assets are properly configured");
            
            // Show user-friendly error in craft button
            if (craftButtonText != null)
            {
                craftButtonText.text = "No Gems Config";
            }
            return null;
        }
        
        if (enableDebugLogging)
        {
            Debug.Log($"[CraftingPanel] Found {allGems.Count} available gems in configuration");
        }
        
        // Calculate tag influences from materials
        var tagInfluences = CalculateTagInfluences(materials);
        
        if (enableDebugLogging)
        {
            Debug.Log($"[CraftingPanel] Calculated {tagInfluences.Count} tag influences");
            foreach (var influence in tagInfluences)
            {
                Debug.Log($"  - {influence.Key}: {influence.Value:F2}");
            }
        }
        
        // Select gem based on influences
        var selectedGemData = SelectGemBasedOnInfluences(allGems, tagInfluences);
        if (selectedGemData == null)
        {
            Debug.LogWarning("[CraftingPanel] No suitable gem found for current material combination");
            Debug.LogWarning("This might indicate an issue with gem selection logic or material configuration");
            
            // Show user-friendly error in craft button
            if (craftButtonText != null)
            {
                craftButtonText.text = "No Gem Match";
            }
            return null;
        }
        
        if (enableDebugLogging)
        {
            Debug.Log($"[CraftingPanel] Selected gem: {selectedGemData.gemName}");
        }
        
        // Calculate rarity based on materials
        var rarity = CalculateGemRarity(materials);
        
        if (enableDebugLogging)
        {
            Debug.Log($"[CraftingPanel] Calculated rarity: {rarity}");
        }
        
        // Create gem instance
        GemInstance gemInstance;
        try
        {
            gemInstance = new GemInstance(selectedGemData, rarity);
            
            if (enableDebugLogging)
            {
                Debug.Log($"[CraftingPanel] Successfully created {rarity} {selectedGemData.gemName}");
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"[CraftingPanel] ERROR: Failed to create GemInstance: {ex.Message}");
            Debug.LogError($"[CraftingPanel] GemData: {selectedGemData?.gemName ?? "null"}, Rarity: {rarity}");
            
            // Show user-friendly error in craft button
            if (craftButtonText != null)
            {
                craftButtonText.text = "Creation Failed";
            }
            return null;
        }
        
        return gemInstance;
    }
    
    private Dictionary<GemTag, float> CalculateTagInfluences(List<CraftingMaterialInstance> materials)
    {
        var influences = new Dictionary<GemTag, float>();
        
        foreach (var material in materials)
        {
            if (material.materialDataTemplate == null)
                continue;
            
            var tags = System.Enum.GetValues(typeof(GemTag));
            foreach (GemTag tag in tags)
            {
                if (tag != GemTag.None && material.materialDataTemplate.influenceTagsBoosted.HasFlag(tag))
                {
                    float boost = material.materialDataTemplate.probabilityBoost * material.stackCount;
                    
                    if (influences.ContainsKey(tag))
                    {
                        influences[tag] += boost;
                    }
                    else
                    {
                        influences[tag] = boost;
                    }
                }
            }
        }
        
        return influences;
    }
    
    private GemData SelectGemBasedOnInfluences(List<GemData> availableGems, Dictionary<GemTag, float> tagInfluences)
    {
        if (availableGems.Count == 0)
            return null;
        
        // Create weighted list of gems
        var weightedGems = new List<(GemData gem, float weight)>();
        
        foreach (var gem in availableGems)
        {
            float weight = 1.0f; // Base weight
            
            // Check if gem has influenced tags
            if (gem is SkillGemData skillGem)
            {
                foreach (var influence in tagInfluences)
                {
                    if (skillGem.gemTags.HasFlag(influence.Key))
                    {
                        weight += influence.Value;
                    }
                }
            }
            else if (gem is SupportGemData supportGem)
            {
                foreach (var influence in tagInfluences)
                {
                    if (supportGem.compatibleTags.HasFlag(influence.Key))
                    {
                        weight += influence.Value;
                    }
                }
            }
            
            weightedGems.Add((gem, weight));
        }
        
        // Select random gem based on weights
        float totalWeight = weightedGems.Sum(wg => wg.weight);
        float randomValue = Random.Range(0f, totalWeight);
        
        float currentWeight = 0f;
        foreach (var weightedGem in weightedGems)
        {
            currentWeight += weightedGem.weight;
            if (randomValue <= currentWeight)
            {
                return weightedGem.gem;
            }
        }
        
        // Fallback to last gem
        return weightedGems.LastOrDefault().gem;
    }
    
    private GemRarity CalculateGemRarity(List<CraftingMaterialInstance> materials)
    {
        float rarityBoostChance = 0f;
        
        // Calculate total rarity boost chance from materials
        foreach (var material in materials)
        {
            if (material.materialDataTemplate != null)
            {
                rarityBoostChance += material.materialDataTemplate.rarityBoostChance * material.stackCount;
            }
        }
        
        // Start with Common and roll for upgrades
        var rarity = GemRarity.Common;
        
        // Each rarity upgrade requires a successful roll
        var rarityLevels = new[] { GemRarity.Uncommon, GemRarity.Rare, GemRarity.Epic, GemRarity.Unique };
        
        foreach (var nextRarity in rarityLevels)
        {
            if (UnityEngine.Random.Range(0f, 1f) < rarityBoostChance)
            {
                rarity = nextRarity;
                rarityBoostChance *= 0.5f; // Reduce chance for next tier
            }
            else
            {
                break;
            }
        }
        
        return rarity;
    }
    
    #endregion
    
    #region Event Handlers
    
    private void OnSlotMaterialChanged(int slotIndex, CraftingMaterialInstance material)
    {
        sockettedMaterials[slotIndex] = material;
        
        // Clear result display since material combination changed
        ClearResultDisplay();
        
        // Reset craft button from any error states
        ResetCraftButtonText();
        
        // Update button state
        UpdateCraftButtonState();
        
        if (enableDebugLogging)
        {
            string materialName = material?.GetDisplayName() ?? "empty";
            Debug.Log($"[CraftingPanel] Slot {slotIndex} material changed: {materialName}");
        }
    }
    
    /// <summary>
    /// Event handler for inventory changes (both splinters and materials).
    /// Updates static display when either inventory changes.
    /// </summary>
    private void OnInventoryChanged()
    {
        // Always refresh the display - we want current values when panel opens
        RefreshStaticMaterialDisplay();
        
        if (enableDebugLogging)
        {
            Debug.Log("[CraftingPanel] Inventory changed - static display updated");
        }
    }
    
    /// <summary>
    /// Event handler for material generation from splinters.
    /// </summary>
    private void OnMaterialGenerated(CraftingMaterialData materialData, SplinterType splinterType, int splintersConsumed)
    {
        if (enableDebugLogging)
        {
            Debug.Log($"[CraftingPanel] Material generated: {materialData.materialName} from {splintersConsumed}x {splinterType}");
        }
        
        // Refresh display to show new material and updated splinter count
        RefreshStaticMaterialDisplay();
    }
    
    #endregion
    
    #region Display Management
    
    private void RefreshDisplay()
    {
        for (int i = 0; i < materialSlots.Count && i < sockettedMaterials.Length; i++)
        {
            if (materialSlots[i] != null)
            {
                materialSlots[i].SetMaterial(sockettedMaterials[i]);
            }
        }
        
        // Update static material display when panel opens
        RefreshStaticMaterialDisplay();
        
        UpdateCraftButtonState();
    }
    
    /// <summary>
    /// Refreshes all MaterialIconButton displays.
    /// Called when panel opens to ensure material counts are up to date.
    /// </summary>
    private void RefreshMaterialIconButtons()
    {
        foreach (var button in materialIconButtons)
        {
            if (button != null)
            {
                button.RefreshDisplay();
            }
        }
        
        if (enableDebugLogging && materialIconButtons.Count > 0)
        {
            Debug.Log($"[CraftingPanel] Refreshed {materialIconButtons.Count} MaterialIconButton displays");
        }
    }
    
    /// <summary>
    /// Updates all static splinter progress and material displays.
    /// Shows separate splinter progress and converted materials.
    /// </summary>
    private void RefreshStaticMaterialDisplay()
    {
        if (SplinterCollectionManager.Instance == null || CraftingMaterialManager.Instance == null)
        {
            return; // Systems not available
        }
        
        // Update splinter progress displays
        UpdateSplinterProgressText(redSplinterText, SplinterType.Red, "Red Splinters");
        UpdateSplinterProgressText(blueSplinterText, SplinterType.Blue, "Blue Splinters");
        UpdateSplinterProgressText(greenSplinterText, SplinterType.Green, "Green Splinters");
        UpdateSplinterProgressText(yellowSplinterText, SplinterType.Yellow, "Yellow Splinters");
        UpdateSplinterProgressText(chaosSplinterText, SplinterType.Chaos, "Chaos Splinters");
        UpdateSplinterProgressText(purpleSplinterText, SplinterType.Purple, "Purple Splinters");
        
        // Update material inventory displays using direct SplinterType → MaterialData lookup
        UpdateMaterialCountBySplinterType(redMaterialText, SplinterType.Red);
        UpdateMaterialCountBySplinterType(blueMaterialText, SplinterType.Blue);
        UpdateMaterialCountBySplinterType(greenMaterialText, SplinterType.Green);
        UpdateMaterialCountBySplinterType(yellowMaterialText, SplinterType.Yellow);
        UpdateMaterialCountBySplinterType(chaosMaterialText, SplinterType.Chaos);
        UpdateMaterialCountBySplinterType(purpleMaterialText, SplinterType.Purple);
        
        if (enableDebugLogging)
        {
            Debug.Log("[CraftingPanel] Refreshed static splinter progress and material displays");
        }
    }
    
    /// <summary>
    /// Updates splinter progress text display.
    /// Format: "23/50"
    /// </summary>
    private void UpdateSplinterProgressText(TextMeshProUGUI textComponent, SplinterType splinterType, string splinterDisplayName)
    {
        if (textComponent == null)
        {
            return; // Text component not assigned
        }
        
        // Get current splinters and requirements
        int splinterCount = SplinterCollectionManager.GetSplinterCount(splinterType);
        int requiredSplinters = SplinterCollectionManager.GetRequiredSplintersForType(splinterType);
        
        // Format: "23/50"
        textComponent.text = $"{splinterCount}/{requiredSplinters}";
    }
    
    /// <summary>
    /// Updates material count text display using robust SplinterType → MaterialData lookup.
    /// Format: "5"
    /// No string matching - uses direct SplinterConfig lookup!
    /// </summary>
    private void UpdateMaterialCountBySplinterType(TextMeshProUGUI textComponent, SplinterType splinterType)
    {
        if (textComponent == null)
        {
            return; // Text component not assigned
        }
        
        // Get the CraftingMaterialData directly from SplinterConfig (no string matching!)
        if (SplinterCollectionManager.Instance != null)
        {
            // Use SplinterCollectionManager to get the material data for this splinter type
            var materialData = GetMaterialDataForSplinterType(splinterType);
            
            if (materialData != null)
            {
                // Get count directly from CraftingMaterialManager using the actual MaterialData reference
                int materialCount = CraftingMaterialManager.GetMaterialCount(materialData);
                textComponent.text = CraftingMaterialManager.FormatMaterialCount(materialCount);
            }
            else
            {
                // Material not configured for this splinter type
                textComponent.text = CraftingMaterialManager.FormatMaterialCount(0);
            }
        }
        else
        {
            textComponent.text = "x0";
        }
    }
    
    /// <summary>
    /// Helper method to get CraftingMaterialData for a SplinterType using SplinterConfig.
    /// This is the robust way - no string matching!
    /// </summary>
    private CraftingMaterialData GetMaterialDataForSplinterType(SplinterType splinterType)
    {
        // Clean API call - no reflection needed!
        return SplinterCollectionManager.GetMaterialDataForSplinterType(splinterType);
    }
    
    /// <summary>
    /// Helper method to find material data by name using existing API.
    /// </summary>
    private CraftingMaterialData FindMaterialDataByName(string materialName)
    {
        var availableMaterials = CraftingMaterialManager.GetAvailableMaterialTypes();
        foreach (var materialData in availableMaterials)
        {
            if (materialData.materialName == materialName)
            {
                return materialData;
            }
        }
        return null;
    }
    
    #endregion
    
    #region Tooltip System
    
    public void ShowTooltip(CraftingMaterialInstance material, Vector3 position)
    {
        if (material == null || tooltipObject == null)
            return;
        
        tooltipObject.SetActive(true);
        tooltipText.text = material.GetTooltipText();
        
        var tooltipRect = tooltipObject.GetComponent<RectTransform>();
        tooltipRect.position = position + new Vector3(100, 0, 0);
    }
    
    public void ShowTooltip(string customTooltipText, Vector3 position)
    {
        if (string.IsNullOrEmpty(customTooltipText) || tooltipObject == null)
            return;
        
        tooltipObject.SetActive(true);
        tooltipText.text = customTooltipText;
        
        var tooltipRect = tooltipObject.GetComponent<RectTransform>();
        tooltipRect.position = position + new Vector3(100, 0, 0);
    }
    
    public void HideTooltip()
    {
        if (tooltipObject != null)
            tooltipObject.SetActive(false);
    }
    
    #endregion
    
    #region Debug Tools
    
    [Title("Debug Tools")]
    [Button("Toggle Crafting Panel")]
    [PropertyOrder(20)]
    private void DebugTogglePanel()
    {
        ToggleCrafting();
    }
    
    [Button("Clear All Materials")]
    [PropertyOrder(21)]
    private void DebugClearMaterials()
    {
        for (int i = 0; i < sockettedMaterials.Length; i++)
        {
            RemoveMaterialFromSlot(i);
        }
        Debug.Log("[CraftingPanel] All materials cleared");
    }
    
    [Button("Log Socketed Materials")]
    [PropertyOrder(22)]
    private void DebugLogMaterials()
    {
        Debug.Log($"[CraftingPanel] Socketed Materials:");
        for (int i = 0; i < sockettedMaterials.Length; i++)
        {
            var material = sockettedMaterials[i];
            string materialInfo = material != null ? $"{material.GetDisplayName()} x{material.stackCount}" : "Empty";
            Debug.Log($"  Slot {i}: {materialInfo}");
        }
    }
    
    [Title("Material Debug Tools")]
    [Button("Refresh Material Display")]
    [PropertyOrder(30)]
    private void DebugRefreshMaterialDisplay()
    {
        RefreshStaticMaterialDisplay();
        Debug.Log("[CraftingPanel] Manually refreshed static material display");
    }
    
    [Button("Add Test Materials")]
    [PropertyOrder(31)]
    private void DebugAddTestMaterials()
    {
        if (CraftingMaterialManager.Instance != null)
        {
            var materialNames = new[] { "Red Crystal", "Blue Crystal", "Green Crystal", "Yellow Crystal", "Chaos Crystal", "Purple Crystal" };
            foreach (var materialName in materialNames)
            {
                var materialData = FindMaterialDataByName(materialName);
                if (materialData != null)
                {
                    CraftingMaterialManager.AddMaterial(materialData, 10);
                }
            }
            Debug.Log("[CraftingPanel] Added 10 of each material type");
        }
        else
        {
            Debug.LogWarning("[CraftingPanel] CraftingMaterialManager not available");
        }
    }
    
    [Button("Log Material Quantities")]
    [PropertyOrder(32)]
    private void DebugLogMaterialQuantities()
    {
        Debug.Log($"[CraftingPanel] === MATERIAL QUANTITIES ===");
        if (CraftingMaterialManager.Instance != null)
        {
            var materialNames = new[] { "Red Crystal", "Blue Crystal", "Green Crystal", "Yellow Crystal", "Chaos Crystal", "Purple Crystal" };
            foreach (var materialName in materialNames)
            {
                var materialData = FindMaterialDataByName(materialName);
                if (materialData != null)
                {
                    int quantity = CraftingMaterialManager.GetMaterialCount(materialData);
                    Debug.Log($"  {materialName}: {quantity}");
                }
                else
                {
                    Debug.Log($"  {materialName}: Not found");
                }
            }
        }
        else
        {
            Debug.Log("  CraftingMaterialManager not available");
        }
    }
    
    [Button("Debug: List All Available Materials")]
    [PropertyOrder(33)]
    private void DebugListAllAvailableMaterials()
    {
        Debug.Log($"[CraftingPanel] === ALL AVAILABLE MATERIALS ===");
        if (CraftingMaterialManager.Instance != null)
        {
            var summary = CraftingMaterialManager.GetMaterialSummary();
            Debug.Log($"Found {summary.Count} material types in inventory:");
            
            foreach (var kvp in summary)
            {
                Debug.Log($"  - Name: '{kvp.Key.materialName}' | Quantity: {kvp.Value}");
            }
            
            var availableTypes = CraftingMaterialManager.GetAvailableMaterialTypes();
            Debug.Log($"Available material types: {availableTypes.Count}");
            foreach (var materialData in availableTypes)
            {
                Debug.Log($"  - Type: '{materialData.materialName}'");
            }
        }
        else
        {
            Debug.LogWarning("[CraftingPanel] CraftingMaterialManager not available");
        }
    }
    
    [Button("Debug: Test Event System")]
    [PropertyOrder(34)]
    private void DebugTestEventSystem()
    {
        Debug.Log($"[CraftingPanel] === TESTING EVENT SYSTEM ===");
        Debug.Log($"enableDebugLogging = {enableDebugLogging}");
        
        // Add a small amount of a dummy material to trigger the event
        if (CraftingMaterialManager.Instance != null)
        {
            var availableTypes = CraftingMaterialManager.GetAvailableMaterialTypes();
            if (availableTypes.Count > 0)
            {
                Debug.Log("Adding 1 of first available material to test events...");
                CraftingMaterialManager.AddMaterial(availableTypes[0], 1);
                Debug.Log("Event should have fired. Check for 'Material inventory changed' message above.");
            }
            else
            {
                Debug.Log("No available material types found. Creating test materials first...");
                DebugAddTestMaterials();
            }
        }
    }
    
    [Button("Debug: Test Splinter Collection")]
    [PropertyOrder(35)]
    private void DebugTestSplinterCollection()
    {
        Debug.Log($"[CraftingPanel] === TESTING SPLINTER COLLECTION ===");
        
        if (SplinterCollectionManager.Instance != null)
        {
            Debug.Log("Adding 10 splinters of each type to test UI updates...");
            var splinterTypes = new[] { SplinterType.Red, SplinterType.Blue, SplinterType.Green, 
                                      SplinterType.Yellow, SplinterType.Chaos, SplinterType.Purple };
            
            foreach (var type in splinterTypes)
            {
                bool success = SplinterCollectionManager.AddSplinters(type, 10);
                int count = SplinterCollectionManager.GetSplinterCount(type);
                Debug.Log($"  {type}: {count} (add success: {success})");
            }
            
            Debug.Log("UI should update automatically via events.");
        }
        else
        {
            Debug.LogWarning("[CraftingPanel] SplinterCollectionManager not found in scene!");
        }
    }
    
    [Button("Debug: Add Test Splinters")]
    [PropertyOrder(36)]
    private void DebugAddTestSplinters()
    {
        Debug.Log($"[CraftingPanel] === ADDING TEST SPLINTERS ===");
        
        if (SplinterCollectionManager.Instance != null)
        {
            var splinterTypes = new[] { SplinterType.Red, SplinterType.Blue, SplinterType.Green, 
                                      SplinterType.Yellow, SplinterType.Chaos, SplinterType.Purple };
            
            foreach (var type in splinterTypes)
            {
                SplinterCollectionManager.AddSplinters(type, 25);
            }
            
            Debug.Log("Added 25 splinters of each type for testing");
        }
        else
        {
            Debug.LogWarning("[CraftingPanel] SplinterCollectionManager not found in scene!");
        }
    }
    
    [Button("Debug: Add 100 Red Splinters (Test Conversion)")]
    [PropertyOrder(37)]
    private void DebugTestMaterialConversion()
    {
        Debug.Log($"[CraftingPanel] === TESTING MATERIAL CONVERSION ===");
        
        if (SplinterCollectionManager.Instance != null)
        {
            // First check SplinterConfig
            Debug.Log("=== CHECKING SPLINTER CONFIG ===");
            bool isRedEnabled = SplinterCollectionManager.IsSplinterTypeEnabled(SplinterType.Red);
            Debug.Log($"Red splinter type enabled: {isRedEnabled}");
            
            Debug.Log("Adding 100 Red splinters to trigger material conversion...");
            bool success = SplinterCollectionManager.AddSplinters(SplinterType.Red, 100);
            Debug.Log($"Add splinters success: {success}");
            
            // Check results
            int redSplinters = SplinterCollectionManager.GetSplinterCount(SplinterType.Red);
            int requiredSplinters = SplinterCollectionManager.GetRequiredSplintersForType(SplinterType.Red);
            Debug.Log($"Remaining Red splinters: {redSplinters}/{requiredSplinters}");
            
            // Check materials
            var materialSummary = CraftingMaterialManager.GetMaterialSummary();
            Debug.Log($"Materials in inventory: {materialSummary.Count} types");
            foreach (var kvp in materialSummary)
            {
                Debug.Log($"  - {kvp.Key.materialName}: {kvp.Value}");
            }
            
            if (materialSummary.Count == 0)
            {
                Debug.LogError("❌ NO MATERIALS GENERATED! SplinterConfig needs to be configured with CraftingMaterialData references!");
            }
        }
        else
        {
            Debug.LogWarning("[CraftingPanel] SplinterCollectionManager not found in scene!");
        }
    }
    
    [Button("Debug: Check SplinterConfig Setup")]
    [PropertyOrder(38)]
    private void DebugCheckSplinterConfig()
    {
        Debug.Log($"[CraftingPanel] === CHECKING SPLINTER CONFIG ===");
        
        if (SplinterCollectionManager.Instance != null)
        {
            var splinterTypes = new[] { SplinterType.Red, SplinterType.Blue, SplinterType.Green, 
                                      SplinterType.Yellow, SplinterType.Chaos, SplinterType.Purple };
                                      
            foreach (var type in splinterTypes)
            {
                bool isEnabled = SplinterCollectionManager.IsSplinterTypeEnabled(type);
                int required = SplinterCollectionManager.GetRequiredSplintersForType(type);
                Debug.Log($"  {type}: Enabled={isEnabled}, Required={required}");
                
                if (!isEnabled)
                {
                    Debug.LogWarning($"    ❌ {type} is NOT configured in SplinterConfig!");
                }
            }
            
            Debug.Log("If any types show 'NOT configured', you need to:");
            Debug.Log("1. Create CraftingMaterialData assets for Red Crystal, Blue Crystal, etc.");
            Debug.Log("2. Assign them in Assets/Data/Currencys/SplinterConfig.asset");
        }
        else
        {
            Debug.LogWarning("[CraftingPanel] SplinterCollectionManager not found in scene!");
        }
    }
    
    [Button("Debug: Show REAL Material Names")]
    [PropertyOrder(39)]
    private void DebugShowRealMaterialNames()
    {
        Debug.Log($"[CraftingPanel] === REAL MATERIAL NAMES IN INVENTORY ===");
        
        if (CraftingMaterialManager.Instance != null)
        {
            var materialSummary = CraftingMaterialManager.GetMaterialSummary();
            Debug.Log($"Found {materialSummary.Count} different material types:");
            
            foreach (var kvp in materialSummary)
            {
                Debug.Log($"  ✅ REAL NAME: '{kvp.Key.materialName}' | Quantity: {kvp.Value}");
            }
            
            Debug.Log("=== NAMES I'M SEARCHING FOR ===");
            var searchNames = new[] { "Red Crystal", "Blue Crystal", "Green Crystal", 
                                    "Yellow Crystal", "Chaos Crystal", "Purple Crystal" };
            foreach (var name in searchNames)
            {
                Debug.Log($"  🔍 SEARCHING: '{name}'");
            }
            
            Debug.Log("❗ If the REAL NAMES don't match SEARCHING names, that's the problem!");
        }
        else
        {
            Debug.LogWarning("[CraftingPanel] CraftingMaterialManager not available");
        }
    }
    
    [Title("MaterialIconButton Debug Tools")]
    [Button("Debug: Refresh MaterialIconButtons")]
    [PropertyOrder(40)]
    private void DebugRefreshMaterialIconButtons()
    {
        RefreshMaterialIconButtons();
        Debug.Log($"[CraftingPanel] Manually refreshed {materialIconButtons.Count} MaterialIconButton displays");
    }
    
    [Button("Debug: Setup MaterialIconButtons")]
    [PropertyOrder(41)]
    private void DebugSetupMaterialIconButtons()
    {
        SetupMaterialIconButtons();
        Debug.Log($"[CraftingPanel] Manually setup MaterialIconButtons - found {materialIconButtons.Count} buttons");
    }
    
    [Button("Debug: List MaterialIconButtons")]
    [PropertyOrder(42)]
    private void DebugListMaterialIconButtons()
    {
        Debug.Log($"[CraftingPanel] === MATERIAL ICON BUTTONS ===");
        Debug.Log($"Found {materialIconButtons.Count} MaterialIconButton components:");
        
        for (int i = 0; i < materialIconButtons.Count; i++)
        {
            var button = materialIconButtons[i];
            if (button != null)
            {
                string materialName = button.MaterialData?.materialName ?? "null";
                int materialCount = button.MaterialCount;
                bool isAvailable = button.IsAvailable;
                
                Debug.Log($"  [{i}] Material: '{materialName}' | Count: {materialCount} | Available: {isAvailable}");
            }
            else
            {
                Debug.Log($"  [{i}] NULL BUTTON");
            }
        }
    }
    
    
    #endregion
}