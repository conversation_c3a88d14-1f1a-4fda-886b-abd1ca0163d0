using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using Sirenix.OdinInspector;

public class SupportGemSlot : MonoBeh<PERSON>our, ISlot, IPointer<PERSON>nter<PERSON><PERSON><PERSON>, IPointerExit<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rag<PERSON><PERSON><PERSON>, IEndDrag<PERSON><PERSON>ler, IDropHandler
{
    [Title("References")]
    [Required]
    public LayeredIconDisplay layeredIconDisplay;
    
    [Required]
    public Image borderImage;
    
    [Required]
    public CanvasGroup canvasGroup;
    
    [Title("Slot Assignment")]
    [Required]
    [InfoBox("Assign the SkillGemSlot this support slot belongs to")]
    public SkillGemSlot parentSkillSlot;
    
    [Title("State")]
    [ReadOnly]
    [ShowInInspector]
    private GemInstance currentSupportGemInstance;
    private InventoryManager inventoryManager;
    
    // ISlot implementation
    public bool IsEmpty => currentSupportGemInstance == null;
    
    // New instance-based properties
    public GemInstance CurrentGemInstance => currentSupportGemInstance;
    
    // Legacy support
    public GemData CurrentGem => currentSupportGemInstance?.gemDataTemplate;
    
    private void Awake()
    {
        // Ensure border is set up for raycasting
        if (borderImage != null)
            borderImage.raycastTarget = true;
            
        // Cache CanvasGroup reference if not assigned
        if (canvasGroup == null)
            canvasGroup = GetComponent<CanvasGroup>();
            
        UpdateVisuals();
    }        
    public void Initialize(SkillGemSlot parent, InventoryManager inventory)
    {
        // parentSkillSlot is now assigned manually in inspector
        inventoryManager = inventory;
    }
    
    public SkillGemSlot GetParentSkillSlot()
    {
        return parentSkillSlot;
    }
    
    // New instance-based methods
    public bool SetSupportGemInstance(GemInstance instance)
    {
        // Early return if setting the same instance (prevents redundant operations)
        if (currentSupportGemInstance == instance)
            return true;
        
        // Check if we're changing a conversion gem
        bool wasConversionGem = currentSupportGemInstance?.gemDataTemplate is SupportGemData oldData && oldData.isConversionGem;
        bool isConversionGem = instance?.gemDataTemplate is SupportGemData newData && newData.isConversionGem;
        
        // Only trigger on ACTUAL changes involving conversion gems
        bool actuallyChanged = (currentSupportGemInstance?.gemDataTemplate != instance?.gemDataTemplate);
        bool conversionGemChanged = actuallyChanged && (wasConversionGem || isConversionGem);
        
        // Allow clearing the slot by passing null
        if (instance == null)
        {
            currentSupportGemInstance = null;
            UpdateVisuals();
            
            // Invalidate cache if we removed a conversion gem
            if (wasConversionGem)
            {
                InvalidateSkillExecutorCache();
            }
            
            return true;
        }

        if (!instance.IsSupportGem)
            return false;
            
        // Validate conversion gem restriction
        if (!ValidateConversionGem(instance))
        {
            Debug.LogWarning($"Cannot equip {instance.DisplayName}: Only one conversion support gem allowed per skill");
            return false;
        }

        currentSupportGemInstance = instance;
        UpdateVisuals();
        
        // Invalidate cache if a conversion gem was added or changed
        if (conversionGemChanged)
        {
            InvalidateSkillExecutorCache();
        }
        
        return true;
    }
    
    public void SetGemInstance(GemInstance instance)
    {
        SetSupportGemInstance(instance);
    }
    
    public GemInstance RemoveGemInstance()
    {
        var instance = currentSupportGemInstance;
        currentSupportGemInstance = null;
        UpdateVisuals();
        return instance;
    }
    
    // Legacy support
    public bool SetSupportGem(GemData gem)
    {
        if (gem == null)
        {
            currentSupportGemInstance = null;
            UpdateVisuals();
            return true;
        }
        
        if (!(gem is SupportGemData))
            return false;
        
        // Create new instance if needed
        GemInstance newInstance = currentSupportGemInstance?.gemDataTemplate != gem ? 
            new GemInstance(gem) : currentSupportGemInstance;
            
        // Validate conversion gem restriction
        if (!ValidateConversionGem(newInstance))
        {
            Debug.LogWarning($"Cannot equip {gem.gemName}: Only one conversion support gem allowed per skill");
            return false;
        }
        
        currentSupportGemInstance = newInstance;
        UpdateVisuals();
        return true;
    }
    
    /// <summary>
    /// Validates that only one conversion support gem can be equipped per skill
    /// </summary>
    private bool ValidateConversionGem(GemInstance newGem)
    {
        // If the new gem is not a conversion gem, it's always valid
        if (!(newGem.gemDataTemplate is SupportGemData newSupportData) || !newSupportData.isConversionGem)
            return true;
            
        // Check if parent skill slot already has a conversion gem in another support slot
        if (parentSkillSlot != null)
        {
            var supportGems = parentSkillSlot.GetSupportGemInstances();
            foreach (var supportGem in supportGems)
            {
                // Skip if it's the current slot (replacing existing gem)
                if (supportGem == currentSupportGemInstance)
                    continue;
                    
                if (supportGem?.gemDataTemplate is SupportGemData supportData && supportData.isConversionGem)
                {
                    // Another conversion gem already exists
                    return false;
                }
            }
        }
        
        return true;
    }
    
    public void SetGem(GemData gem)
    {
        SetSupportGem(gem);
    }
    
    public GemData RemoveGem()
    {
        var template = currentSupportGemInstance?.gemDataTemplate;
        currentSupportGemInstance = null;
        UpdateVisuals();
        return template;
    }
    
    private void UpdateVisuals()
    {
        if (currentSupportGemInstance != null)
        {
            layeredIconDisplay.SetGem(currentSupportGemInstance);
            // borderImage.color remains unchanged
            borderImage.raycastTarget = true;  // Ensure the border can receive pointer events
            
            // TODO: Add level/quality display overlay
        }
        else
        {
            layeredIconDisplay.ClearIcon();
            // borderImage.color remains unchanged
            borderImage.raycastTarget = true;  // Keep raycast active
        }
    }
    
    public void SetAlpha(float alpha)
    {
        if (canvasGroup != null)
        {
            canvasGroup.alpha = alpha;
        }
    }
    
    public void SetAvailable(bool available)
    {
        SetAlpha(available ? 1.0f : 0.3f);
    }        
    
    public void OnPointerEnter(PointerEventData eventData)
    {
        if (currentSupportGemInstance != null)
        {
            // Ensure we have a valid inventory manager
            if (inventoryManager == null)
            {
                inventoryManager = FindFirstObjectByType<InventoryManager>();
            }
            
            if (inventoryManager != null)
            {
                inventoryManager.ShowTooltip(currentSupportGemInstance, transform.position);
            }
        }
    }
    
    public void OnPointerExit(PointerEventData eventData)
    {
        // Ensure we have a valid inventory manager
        if (inventoryManager == null)
        {
            inventoryManager = FindFirstObjectByType<InventoryManager>();
        }
        
        if (inventoryManager != null)
        {
            inventoryManager.HideTooltip();
        }
    }
    
    // Drag & Drop implementation
    public void OnBeginDrag(PointerEventData eventData)
    {
        if (DragDropManager.Instance != null)
            DragDropManager.Instance.StartDrag(this, eventData);
    }
    
    public void OnDrag(PointerEventData eventData)
    {
        if (DragDropManager.Instance != null)
            DragDropManager.Instance.UpdateDragPosition(eventData);
    }
    
    public void OnEndDrag(PointerEventData eventData)
    {
        if (DragDropManager.Instance != null)
            DragDropManager.Instance.EndDrag(eventData);
    }
    
    public void OnDrop(PointerEventData eventData)
    {
        // Drop is handled by DragDropManager in EndDrag
    }
    
    /// <summary>
    /// Invalidates the SkillExecutor cache for the parent skill slot
    /// This is critical for autonomous skills to pick up conversion gem changes
    /// </summary>
    private void InvalidateSkillExecutorCache()
    {
        if (parentSkillSlot == null) return;
        
        // Find the SkillExecutor in the scene
        var skillExecutor = UnityEngine.Object.FindFirstObjectByType<SkillExecutor>();
        if (skillExecutor == null) return;
        
        // Get the slot index from the parent skill slot
        // This assumes the parent skill slot has a way to identify its index
        // We need to find the equipment panel and get the index
        var equipmentPanel = parentSkillSlot.GetComponentInParent<EquipmentPanel>();
        if (equipmentPanel == null) return;
        
        // Find the index of this skill slot in the equipment panel
        var skillSlots = equipmentPanel.GetComponentsInChildren<SkillGemSlot>();
        for (int i = 0; i < skillSlots.Length; i++)
        {
            if (skillSlots[i] == parentSkillSlot)
            {
                // Invalidate the cache for this slot index
                skillExecutor.InvalidateCache(i);
                break;
            }
        }
    }
}
