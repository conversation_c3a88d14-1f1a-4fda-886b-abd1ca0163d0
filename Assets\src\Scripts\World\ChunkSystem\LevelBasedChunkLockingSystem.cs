using UnityEngine;
using System.Collections.Generic;
using Sirenix.OdinInspector;

/// <summary>
/// Manages zone-based chunk progression with optimized 3-boundary system
/// Zone levels represent distance from starting position, NOT player character level
/// </summary>
public class LevelBasedChunkLockingSystem : MonoBehaviour
{
    [Title("Configuration")]
    [SerializeField]
    [Required]
    private TilemapChunkManager chunkManager;
    
    [SerializeField]
    [Required]
    [Tooltip("Prefab with Collider2D component for chunk boundaries")]
    private GameObject boundaryPrefab;
    
    [SerializeField]
    [Tooltip("Parent transform for boundary objects")]
    private Transform boundariesParent;
    
    [Title("Zone System Settings")]
    [SerializeField]
    [Tooltip("Enable debug visualization")]
    private bool showDebugInfo = false;
    
    [Title("Runtime Info")]
    [ShowInInspector, ReadOnly]
    private int currentZoneLevel = 0;
    
    [ShowInInspector, ReadOnly]
    private ChunkCoordinate startingChunk;
    
    [ShowInInspector, ReadOnly]
    private ChunkCoordinate currentPlayerChunk;
    
    [ShowInInspector, ReadOnly]
    private int highestLockedZoneLevel = -1; // Highest locked zone level (-1 = none)
    
    // Zone level tracking
    private Dictionary<int, HashSet<ChunkCoordinate>> chunksByZoneLevel = new Dictionary<int, HashSet<ChunkCoordinate>>();
    private HashSet<ChunkCoordinate> visitedChunksCurrentZone = new HashSet<ChunkCoordinate>();
    private HashSet<ChunkCoordinate> allLockedChunks = new HashSet<ChunkCoordinate>();
    
    // Boundary management (only 3 boundaries total)
    private GameObject permanentBoundary; // Grows with each level
    private GameObject dynamicBoundary1;  // First pooled boundary
    private GameObject dynamicBoundary2;  // Second pooled boundary
    
    private Collider2D permanentBoundaryCollider;
    private Collider2D dynamicBoundaryCollider1;
    private Collider2D dynamicBoundaryCollider2;
    
    // Previous position tracking for dynamic boundaries
    private ChunkCoordinate lastVisitedChunk;
    private ChunkCoordinate zoneEntryChunk; // Where player entered current zone
    
    private bool isInitialized = false;
    
    // Public access for other systems
    public ChunkCoordinate StartingChunk => startingChunk;
    public bool IsInitialized => isInitialized;

    void Start()
    {
        if (chunkManager == null)
        {
            chunkManager = TilemapChunkManager.Instance;
            if (chunkManager == null)
            {
                Debug.LogError("LevelBasedChunkLockingSystem: No TilemapChunkManager found!");
                enabled = false;
                return;
            }
        }
        
        // Create boundaries parent if not set
        if (boundariesParent == null)
        {
            GameObject parentObj = new GameObject("LevelBoundaries");
            parentObj.transform.SetParent(transform);
            boundariesParent = parentObj.transform;
        }
        
        Initialize();
    }
    
    void Initialize()
    {
        if (isInitialized) return;
        
        startingChunk = chunkManager.GetCurrentPlayerChunk();
        currentPlayerChunk = startingChunk;
        lastVisitedChunk = startingChunk;
        
        // Initialize zone level tracking
        currentZoneLevel = CalculateZoneLevel(startingChunk);
        
        // Create the 3 boundaries
        CreateBoundaries();
        
        // Mark starting chunk as visited
        MarkChunkVisited(startingChunk);
        
        isInitialized = true;
        
        if (showDebugInfo)
        {
            Debug.Log($"LevelBasedChunkLockingSystem: Initialized at chunk {startingChunk}, zone {currentZoneLevel}");
        }
    }
    
    void Update()
    {
        if (!isInitialized) return;
        
        ChunkCoordinate playerChunk = chunkManager.GetCurrentPlayerChunk();
        
        if (playerChunk != currentPlayerChunk)
        {
            OnPlayerChunkChanged(currentPlayerChunk, playerChunk);
        }
    }
    
    void OnPlayerChunkChanged(ChunkCoordinate fromChunk, ChunkCoordinate toChunk)
    {
        int fromZoneLevel = CalculateZoneLevel(fromChunk);
        int toZoneLevel = CalculateZoneLevel(toChunk);
        
        lastVisitedChunk = currentPlayerChunk;
        currentPlayerChunk = toChunk;
        
        if (showDebugInfo)
        {
            Debug.Log($"Player moved from {fromChunk} (Zone {fromZoneLevel}) to {toChunk} (Zone {toZoneLevel})");
        }
        
        // Check if player is moving to a locked chunk (shouldn't happen with boundaries)
        if (allLockedChunks.Contains(toChunk))
        {
            Debug.LogWarning($"Player entered locked chunk {toChunk}! This shouldn't happen with boundaries.");
            return;
        }
        
        // Handle zone transitions - no max zone limit
        if (toZoneLevel > fromZoneLevel)
        {
            // Moving to higher zone - lock entire previous zone
            OnPlayerMovedToHigherZone(fromZoneLevel, toZoneLevel);
        }
        else if (toZoneLevel == fromZoneLevel)
        {
            // Moving within same zone - lock only the previous chunk
            OnPlayerMovedWithinZone(fromChunk, toChunk, toZoneLevel);
        }
        else
        {
            // Moving to lower zone - this should be blocked by boundaries
            Debug.LogError($"Player moved to lower zone! From Zone {fromZoneLevel} to Zone {toZoneLevel}. Boundaries failed!");
        }
        
        // Update current zone level
        currentZoneLevel = toZoneLevel;
    }
    
    void OnPlayerMovedToHigherZone(int fromZone, int toZone)
    {
        if (showDebugInfo)
        {
            Debug.Log($"=== ZONE TRANSITION: Zone {fromZone} -> Zone {toZone} ===");
        }
        
        // Lock entire previous zone
        LockEntireZone(fromZone);
        
        // Update zone entry point
        zoneEntryChunk = currentPlayerChunk;
        
        // Clear visited chunks for new zone
        visitedChunksCurrentZone.Clear();
        visitedChunksCurrentZone.Add(currentPlayerChunk);
        
        // Update boundaries
        UpdateBoundariesForZoneTransition(fromZone, toZone);
    }
    
    void OnPlayerMovedWithinZone(ChunkCoordinate fromChunk, ChunkCoordinate toChunk, int zoneLevel)
    {
        if (showDebugInfo)
        {
            Debug.Log($"Moving within zone {zoneLevel}: {fromChunk} -> {toChunk}");
        }
        
        // Check if player is leaving the zone entry chunk for the first time
        if (fromChunk.Equals(zoneEntryChunk) && !dynamicBoundary1.activeSelf)
        {
            // Place the first dynamic boundary at the entry point
            PlaceDynamicBoundary(dynamicBoundaryCollider1, fromChunk);
            
            if (showDebugInfo)
            {
                Debug.Log($"Placed zone entry boundary at {fromChunk} - prevents circular return to zone {zoneLevel} entry");
            }
            
            // Don't lock the entry chunk itself - the boundary handles blocking
        }
        else if (!fromChunk.Equals(zoneEntryChunk))
        {
            // Lock the previous chunk normally (but not the entry chunk)
            LockSingleChunk(fromChunk);
        }
        
        // Mark new chunk as visited
        MarkChunkVisited(toChunk);
        
        // Update dynamic boundaries for within-zone movement
        UpdateDynamicBoundariesForWithinZone(fromChunk, toChunk, zoneLevel);
    }
    
    void LockEntireZone(int zoneLevel)
    {
        if (zoneLevel > highestLockedZoneLevel)
        {
            highestLockedZoneLevel = zoneLevel;
        }
        
        // Get all chunks at this zone level
        var chunksToLock = GetAllChunksAtZoneLevel(zoneLevel);
        
        foreach (var chunk in chunksToLock)
        {
            allLockedChunks.Add(chunk);
        }
        
        if (showDebugInfo)
        {
            Debug.Log($"Locked entire zone {zoneLevel} ({chunksToLock.Count} chunks)");
        }
    }
    
    void LockSingleChunk(ChunkCoordinate chunk)
    {
        allLockedChunks.Add(chunk);
        
        if (showDebugInfo)
        {
            Debug.Log($"Locked single chunk: {chunk}");
        }
    }
    
    void MarkChunkVisited(ChunkCoordinate chunk)
    {
        int zoneLevel = CalculateZoneLevel(chunk);
        
        // Track by zone level
        if (!chunksByZoneLevel.ContainsKey(zoneLevel))
        {
            chunksByZoneLevel[zoneLevel] = new HashSet<ChunkCoordinate>();
        }
        chunksByZoneLevel[zoneLevel].Add(chunk);
        
        // Track for current zone
        visitedChunksCurrentZone.Add(chunk);
    }
    
    void CreateBoundaries()
    {
        // Create permanent boundary (not pooled)
        permanentBoundary = Instantiate(boundaryPrefab, boundariesParent);
        permanentBoundary.name = "PermanentLevelBoundary";
        permanentBoundaryCollider = permanentBoundary.GetComponent<Collider2D>();
        // Note: Boundary initialization removed - using standard Unity colliders
        permanentBoundary.SetActive(false); // Start inactive
        
        // Get pooled boundaries
        if (PoolManager.Instance != null)
        {
            dynamicBoundary1 = PoolManager.Instance.Spawn(boundaryPrefab, Vector3.zero, Quaternion.identity, boundariesParent);
            dynamicBoundary2 = PoolManager.Instance.Spawn(boundaryPrefab, Vector3.zero, Quaternion.identity, boundariesParent);
        }
        else
        {
            // Fallback if pool manager not available
            dynamicBoundary1 = Instantiate(boundaryPrefab, boundariesParent);
            dynamicBoundary2 = Instantiate(boundaryPrefab, boundariesParent);
        }
        
        dynamicBoundaryCollider1 = dynamicBoundary1.GetComponent<Collider2D>();
        dynamicBoundaryCollider2 = dynamicBoundary2.GetComponent<Collider2D>();
        
        // Note: Boundary initialization removed - using standard Unity colliders
        
        // Start with dynamic boundaries inactive
        dynamicBoundary1.SetActive(false);
        dynamicBoundary2.SetActive(false);
    }
    
    void UpdateBoundariesForZoneTransition(int fromZone, int toZone)
    {
        // Update permanent boundary to encompass all locked zones
        UpdatePermanentBoundary();
        
        // Reset dynamic boundaries for the new zone
        // Dynamic boundary 1: Will be placed when player LEAVES the entry chunk
        dynamicBoundary1.SetActive(false);
        
        // Dynamic boundary 2: Initially inactive, will be used for within-zone blocking
        dynamicBoundary2.SetActive(false);
    }
    
    void UpdateDynamicBoundariesForWithinZone(ChunkCoordinate fromChunk, ChunkCoordinate toChunk, int zoneLevel)
    {
        // Only use boundary 2 if we're not placing boundary 1
        if (!fromChunk.Equals(zoneEntryChunk))
        {
            // Use dynamic boundary 2 to block the previous position
            PlaceDynamicBoundary(dynamicBoundaryCollider2, fromChunk);
        }
        
        // If we need to prevent circular movement, we might need to reposition boundary 1
        // This is where smart logic would determine optimal boundary placement
    }
    
    void UpdatePermanentBoundary()
    {
        if (highestLockedZoneLevel < 0)
        {
            // No zones locked yet
            permanentBoundary.SetActive(false);
            return;
        }
        
        // Calculate bounds that encompass all locked zones
        int chunkWidth = chunkManager.GetChunkWidth();
        int chunkHeight = chunkManager.GetChunkHeight();
        float maxChunkSize = Mathf.Max(chunkWidth, chunkHeight);
        
        // The permanent boundary should block all chunks at distance <= highestLockedZoneLevel
        float boundaryRadius = (highestLockedZoneLevel + 0.5f) * maxChunkSize;
        
        // Get the CENTER of the starting chunk (ToWorldPosition gives bottom-left corner)
        Vector3 bottomLeft = startingChunk.ToWorldPosition(chunkWidth, chunkHeight);
        Vector3 center = bottomLeft + new Vector3(chunkWidth / 2f, chunkHeight / 2f, 0f);
        
        Bounds zoneBounds = new Bounds(center, new Vector3(boundaryRadius * 2, boundaryRadius * 2, 1));
        
        // Set collider bounds - Unity BoxCollider2D uses size, not bounds
        if (permanentBoundaryCollider is BoxCollider2D boxCollider)
        {
            boxCollider.size = zoneBounds.size;
            boxCollider.offset = Vector2.zero; // Center at transform position
        }
        
        // Position the boundary at the calculated center
        permanentBoundary.transform.position = center;
        
        // Note: Custom boundary initialization removed with collision system
        permanentBoundary.SetActive(true);
        
        if (showDebugInfo)
        {
            Debug.Log($"Updated permanent boundary to block zones 0-{highestLockedZoneLevel} (size: {zoneBounds.size})");
        }
    }
    
    void PlaceDynamicBoundary(Collider2D boundary, ChunkCoordinate chunk)
    {
        Bounds chunkBounds = chunkManager.GetChunkBounds(chunk);
        
        // Position the boundary at the chunk center
        boundary.transform.position = chunkBounds.center;
        
        // Scale the boundary to match chunk size (if it's a BoxCollider2D)
        if (boundary is BoxCollider2D boxCollider)
        {
            boxCollider.size = chunkBounds.size;
        }
        
        boundary.gameObject.SetActive(true);
        
        if (showDebugInfo)
        {
            Debug.Log($"Placed dynamic boundary at chunk {chunk}");
        }
    }
    
    public int CalculateZoneLevel(ChunkCoordinate chunk)
    {
        // Zone level = max distance from starting chunk (Chebyshev distance)
        int dx = Mathf.Abs(chunk.x - startingChunk.x);
        int dy = Mathf.Abs(chunk.y - startingChunk.y);
        return Mathf.Max(dx, dy);
    }
    
    public bool IsChunkLocked(ChunkCoordinate chunk)
    {
        return allLockedChunks.Contains(chunk);
    }
    
    List<ChunkCoordinate> GetAllChunksAtZoneLevel(int zoneLevel)
    {
        List<ChunkCoordinate> chunks = new List<ChunkCoordinate>();
        
        // Zone level forms a square ring around the starting chunk
        for (int x = -zoneLevel; x <= zoneLevel; x++)
        {
            for (int y = -zoneLevel; y <= zoneLevel; y++)
            {
                // Check if this chunk is exactly at the specified zone level
                if (Mathf.Max(Mathf.Abs(x), Mathf.Abs(y)) == zoneLevel)
                {
                    ChunkCoordinate chunk = new ChunkCoordinate(
                        startingChunk.x + x,
                        startingChunk.y + y
                    );
                    chunks.Add(chunk);
                }
            }
        }
        
        return chunks;
    }
    
    #region Debug Visualization
    
    void OnDrawGizmos()
    {
        if (!showDebugInfo || !isInitialized) return;
        
        int chunkWidth = chunkManager != null ? chunkManager.GetChunkWidth() : 32;
        int chunkHeight = chunkManager != null ? chunkManager.GetChunkHeight() : 32;
        
        // Draw locked chunks
        Gizmos.color = new Color(1f, 0f, 0f, 0.3f);
        foreach (var chunk in allLockedChunks)
        {
            Bounds bounds = GetChunkBounds(chunk, chunkWidth, chunkHeight);
            Gizmos.DrawCube(bounds.center, bounds.size);
        }
        
        // Draw current zone chunks
        Gizmos.color = new Color(0f, 1f, 0f, 0.2f);
        foreach (var chunk in visitedChunksCurrentZone)
        {
            if (!allLockedChunks.Contains(chunk))
            {
                Bounds bounds = GetChunkBounds(chunk, chunkWidth, chunkHeight);
                Gizmos.DrawCube(bounds.center, bounds.size);
            }
        }
        
        // Draw zone boundaries
        for (int zone = 0; zone <= currentZoneLevel + 1; zone++)
        {
            float radius = (zone + 0.5f) * Mathf.Max(chunkWidth, chunkHeight);
            
            if (zone <= highestLockedZoneLevel)
                Gizmos.color = Color.red; // Locked zone
            else if (zone == currentZoneLevel)
                Gizmos.color = Color.green; // Current zone
            else
                Gizmos.color = Color.yellow; // Next zone
                
            // Get center of starting chunk for drawing
            Vector3 bottomLeft = startingChunk.ToWorldPosition(chunkWidth, chunkHeight);
            Vector3 center = bottomLeft + new Vector3(chunkWidth / 2f, chunkHeight / 2f, 0f);
            DrawSquare(center, radius);
        }
    }
    
    void DrawSquare(Vector3 center, float radius)
    {
        Vector3 topLeft = center + new Vector3(-radius, radius, 0);
        Vector3 topRight = center + new Vector3(radius, radius, 0);
        Vector3 bottomRight = center + new Vector3(radius, -radius, 0);
        Vector3 bottomLeft = center + new Vector3(-radius, -radius, 0);
        
        Gizmos.DrawLine(topLeft, topRight);
        Gizmos.DrawLine(topRight, bottomRight);
        Gizmos.DrawLine(bottomRight, bottomLeft);
        Gizmos.DrawLine(bottomLeft, topLeft);
    }
    
    Bounds GetChunkBounds(ChunkCoordinate chunk, int width, int height)
    {
        Vector3 pos = chunk.ToWorldPosition(width, height);
        return new Bounds(pos + new Vector3(width/2f, height/2f, 0), new Vector3(width, height, 0));
    }
    
    #endregion
    
    #region Debug Commands
    
    [Button("Debug: Show Zone Status")]
    public void DebugShowZoneStatus()
    {
        Debug.Log("=== ZONE-BASED CHUNK SYSTEM STATUS ===");
        Debug.Log($"Current Zone Level: {currentZoneLevel}");
        Debug.Log($"Current Player Chunk: {currentPlayerChunk}");
        Debug.Log($"Locked Zones: 0 to {highestLockedZoneLevel}");
        Debug.Log($"Total Locked Chunks: {allLockedChunks.Count}");
        Debug.Log($"Visited Chunks (Current Zone): {visitedChunksCurrentZone.Count}");
        
        Debug.Log("\nBoundary Status:");
        Debug.Log($"- Permanent Boundary: {(permanentBoundary != null && permanentBoundary.activeSelf ? "Active" : "Inactive")}");
        Debug.Log($"- Dynamic Boundary 1: {(dynamicBoundary1 != null && dynamicBoundary1.activeSelf ? "Active" : "Inactive")}");
        Debug.Log($"- Dynamic Boundary 2: {(dynamicBoundary2 != null && dynamicBoundary2.activeSelf ? "Active" : "Inactive")}");
        Debug.Log("=====================================");
    }
    
    [Button("Reset Zone System")]
    public void ResetZoneSystem()
    {
        // Clear all tracking
        chunksByZoneLevel.Clear();
        visitedChunksCurrentZone.Clear();
        allLockedChunks.Clear();
        
        // Reset state
        currentZoneLevel = 0;
        highestLockedZoneLevel = -1;
        currentPlayerChunk = startingChunk;
        lastVisitedChunk = startingChunk;
        
        // Reset boundaries
        if (permanentBoundary != null)
            permanentBoundary.SetActive(false);
        if (dynamicBoundary1 != null)
            dynamicBoundary1.SetActive(false);
        if (dynamicBoundary2 != null)
            dynamicBoundary2.SetActive(false);
            
        // Re-mark starting chunk
        MarkChunkVisited(startingChunk);
        
        Debug.Log("Zone-based chunk system reset!");
    }
    
    #endregion
}