fileFormatVersion: 2
guid: b994b3550050fe14f9ec2b5738272787
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 8020143640844766479
    second: spritesheet_0
  - first:
      213: -6251682230944352461
    second: spritesheet_1
  - first:
      213: -1259408508766424310
    second: spritesheet_2
  - first:
      213: -5962389079421231736
    second: spritesheet_3
  - first:
      213: 2788568348960109317
    second: spritesheet_4
  - first:
      213: 226702753830753226
    second: spritesheet_5
  - first:
      213: -7921052967969465774
    second: spritesheet_6
  - first:
      213: -1370661682523340017
    second: spritesheet_7
  - first:
      213: -8276262488406422588
    second: spritesheet_8
  - first:
      213: -5906973516983679737
    second: spritesheet_9
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 32
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: spritesheet_0
      rect:
        serializedVersion: 2
        x: 0
        y: 1
        width: 16
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f0d092816264d4f60800000000000000
      internalID: 8020143640844766479
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spritesheet_1
      rect:
        serializedVersion: 2
        x: 16
        y: 0
        width: 17
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 337b0ff32209d39a0800000000000000
      internalID: -6251682230944352461
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spritesheet_2
      rect:
        serializedVersion: 2
        x: 31
        y: 0
        width: 66
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a037fd742aea58ee0800000000000000
      internalID: -1259408508766424310
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spritesheet_3
      rect:
        serializedVersion: 2
        x: 96
        y: 0
        width: 16
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 88ddd6ce8b6514da0800000000000000
      internalID: -5962389079421231736
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spritesheet_4
      rect:
        serializedVersion: 2
        x: 112
        y: 1
        width: 16
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5033aaf832cf2b620800000000000000
      internalID: 2788568348960109317
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spritesheet_5
      rect:
        serializedVersion: 2
        x: 128
        y: 1
        width: 16
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ac35fa714f8652300800000000000000
      internalID: 226702753830753226
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spritesheet_6
      rect:
        serializedVersion: 2
        x: 143
        y: 0
        width: 17
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 25a502d0b44c21290800000000000000
      internalID: -7921052967969465774
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spritesheet_7
      rect:
        serializedVersion: 2
        x: 159
        y: 0
        width: 66
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f0305f9367e6afce0800000000000000
      internalID: -1370661682523340017
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spritesheet_8
      rect:
        serializedVersion: 2
        x: 224
        y: 0
        width: 16
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4cb05a6cd1fc42d80800000000000000
      internalID: -8276262488406422588
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spritesheet_9
      rect:
        serializedVersion: 2
        x: 240
        y: 1
        width: 16
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7054c8ef1e6360ea0800000000000000
      internalID: -5906973516983679737
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      spritesheet_0: 8020143640844766479
      spritesheet_1: -6251682230944352461
      spritesheet_2: -1259408508766424310
      spritesheet_3: -5962389079421231736
      spritesheet_4: 2788568348960109317
      spritesheet_5: 226702753830753226
      spritesheet_6: -7921052967969465774
      spritesheet_7: -1370661682523340017
      spritesheet_8: -8276262488406422588
      spritesheet_9: -5906973516983679737
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
